import {
  Users,
  Calendar,
  FileText,
  BarChart3,
  Settings,
  Bell,
  Activity,
  DollarSign,
  UserCheck,
  Archive,
  TrendingUp,
  Shield,
} from 'lucide-react';
import type { Patient, Appointment, Staff, AgendaEvent, Payment, Insurance, InventoryItem, Notification } from './types';

export const user = {
  name: '<PERSON><PERSON><PERSON>',
  role: '<PERSON><PERSON><PERSON>',
  specialty: 'Cardiologia',
  avatar: 'https://placehold.co/100x100.png',
  permissions: ['admin', 'professional', 'receptionist'],
  email: '<EMAIL>',
};

const calculateAge = (birthDate: string): number => {
  const birth = new Date(birthDate);
  const today = new Date();
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  return age;
};

const isBirthdayToday = (birthDate: string): boolean => {
  const birth = new Date(birthDate);
  const today = new Date();
  return birth.getMonth() === today.getMonth() && birth.getDate() === today.getDate();
};

const isBirthdayThisWeek = (birthDate: string): boolean => {
    const birth = new Date(birthDate);
    const today = new Date();
    const oneWeekFromNow = new Date();
    oneWeekFromNow.setDate(today.getDate() + 7);

    const thisYearBirthday = new Date(today.getFullYear(), birth.getMonth(), birth.getDate());
    
    if (thisYearBirthday >= today && thisYearBirthday <= oneWeekFromNow) {
        return true;
    }
    
    const nextYearBirthday = new Date(today.getFullYear() + 1, birth.getMonth(), birth.getDate());
    return nextYearBirthday >= today && nextYearBirthday <= oneWeekFromNow;
};

const initialPatients: Omit<Patient, 'age' | 'isBirthday' | 'birthdayThisWeek' | 'avatar' | 'paymentType'>[] = [
  {
    id: 1,
    name: 'João Silva',
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
    address: 'Rua das Flores, 123 - Centro',
    birthDate: '1985-03-15',
    cpf: '123.456.789-00',
    bloodType: 'O+',
    allergies: ['Penicilina'],
    emergencyContact: {
      name: 'Maria Silva',
      phone: '(11) 88888-8888',
      relationship: 'Esposa',
    },
    lastVisit: '2024-05-15',
    status: 'ativo',
    medicalHistory: `Data: 15/05/2024
Queixa: Dor de cabeça persistente e tontura.
Diagnóstico: Enxaqueca com aura.
Tratamento: Prescrito Sumatriptano 50mg. Recomendado repouso e evitar gatilhos.
---
Data: 10/01/2024
Queixa: Check-up anual.
Diagnóstico: Saúde geral boa. Hipertensão controlada com medicação (Losartana 50mg).
Tratamento: Manter medicação. Praticar atividade física regular.`,
    insurance: 'Amil',
    totalAppointments: 12,
    profession: 'Engenheiro',
    interests: ['Cardiologia', 'Prevenção'],
    interactions: 12,
  },
  {
    id: 2,
    name: 'Ana Oliveira',
    email: '<EMAIL>',
    phone: '(11) 88888-8888',
    address: 'Av. Principal, 456 - Jardim Europa',
    birthDate: '1990-07-22',
    cpf: '987.654.321-00',
    bloodType: 'A+',
    allergies: ['Dipirona'],
    emergencyContact: {
      name: 'Carlos Oliveira',
      phone: '(11) 77777-7777',
      relationship: 'Marido',
    },
    lastVisit: '2024-05-10',
    status: 'ativo',
    medicalHistory: `Data: 10/05/2024
Queixa: Acompanhamento de diabetes tipo 2.
Diagnóstico: Glicemia de jejum elevada (140 mg/dL).
Tratamento: Ajuste na dose de Metformina para 850mg 2x ao dia. Encaminhada para nutricionista.
---
Data: 05/11/2023
Queixa: Gripe forte com febre.
Diagnóstico: Infecção por Influenza.
Tratamento: Oseltamivir (Tamiflu) e repouso.`,
    insurance: 'Bradesco Saúde',
    totalAppointments: 8,
    profession: 'Professora',
    interests: ['Endocrinologia', 'Nutrição'],
    interactions: 8,
  },
  {
    id: 3,
    name: 'Carlos Mendes',
    email: '<EMAIL>',
    phone: '(11) 77777-7777',
    address: 'Rua do Comércio, 789 - Vila Nova',
    birthDate: '1978-12-05',
    cpf: '456.789.123-00',
    bloodType: 'B-',
    allergies: ['Nenhuma'],
    emergencyContact: {
      name: 'Lucia Mendes',
      phone: '(11) 66666-6666',
      relationship: 'Mãe',
    },
    lastVisit: '2024-05-20',
    status: 'ativo',
    medicalHistory: `Data: 20/05/2024
Queixa: Dores nas costas.
Diagnóstico: Lombalgia mecânica.
Tratamento: Fisioterapia e anti-inflamatórios (Ibuprofeno 400mg).`,
    insurance: 'Unimed',
    totalAppointments: 15,
    profession: 'Comerciante',
    interests: ['Clínica Geral', 'Check-up'],
    interactions: 15,
  },
];

export const patients: Patient[] = initialPatients.map(p => ({
  ...p,
  age: calculateAge(p.birthDate),
  isBirthday: isBirthdayToday(p.birthDate),
  birthdayThisWeek: isBirthdayThisWeek(p.birthDate),
  avatar: `https://placehold.co/100x100.png`,
  paymentType: p.insurance ? 'convenio' : 'particular'
}));

export const appointments: Appointment[] = [
  {
    id: 1,
    patientId: 1,
    patientName: 'João Silva',
    doctorName: 'Dra. Maria Santos',
    date: '2024-07-15',
    time: '09:00',
    duration: 30,
    type: 'Consulta',
    specialty: 'Cardiologia',
    status: 'agendado',
    notes: 'Consulta de rotina - verificar pressão arterial',
    room: 'Sala 101',
  },
  {
    id: 2,
    patientId: 2,
    patientName: 'Ana Oliveira',
    doctorName: 'Dr. Pedro Costa',
    date: '2024-07-16',
    time: '14:30',
    duration: 45,
    type: 'Exame',
    specialty: 'Endocrinologia',
    status: 'confirmado',
    notes: 'Exame de glicemia e acompanhamento diabetes',
    room: 'Sala 203',
  },
  {
    id: 3,
    patientId: 3,
    patientName: 'Carlos Mendes',
    doctorName: 'Dra. Ana Silva',
    date: '2024-07-17',
    time: '11:00',
    duration: 60,
    type: 'Procedimento',
    specialty: 'Clínica Geral',
    status: 'realizado',
    notes: 'Exames de rotina e avaliação colesterol',
    room: 'Sala 105',
  },
];

export const staff: Staff[] = [
  {
    id: 1,
    name: 'Dra. Maria Santos',
    role: ['Diretora Médica', 'Cardiologista'],
    email: '<EMAIL>',
    phone: '(11) 98888-0000',
    avatar: 'https://placehold.co/100x100.png',
    crm: 'CRM/SP 123450',
    admissionDate: '2018-03-10',
    salary: 25000,
    shift: 'integral',
    status: 'ativo',
    accessLevel: 'administrador',
    address: 'Avenida Principal, 123 - Centro',
    specialty: 'Cardiologia',
    permissions: ['admin', 'professional']
  },
  {
    id: 2,
    name: 'Dr. Pedro Costa',
    role: ['Cardiologista'],
    email: '<EMAIL>',
    phone: '(11) 98888-0001',
    avatar: 'https://placehold.co/100x100.png',
    crm: 'CRM/SP 123456',
    admissionDate: '2020-01-15',
    salary: 15000,
    shift: 'integral',
    status: 'ativo',
    accessLevel: 'avancado',
    address: 'Rua das Palmeiras, 456 - Centro',
    specialty: 'Cardiologia',
    permissions: ['professional']
  },
  {
    id: 3,
    name: 'Enf. Ana Silva',
    role: ['Enfermeira Chefe', 'Neonatologia'],
    email: '<EMAIL>',
    phone: '(11) 98888-0002',
    avatar: 'https://placehold.co/100x100.png',
    crm: 'COREN/SP 654321',
    admissionDate: '2019-05-20',
    salary: 6500,
    shift: 'manha',
    status: 'ativo',
    accessLevel: 'intermediario',
    address: 'Avenida Central, 789 - Vila Nova',
    specialty: 'Enfermagem Geral',
    permissions: ['receptionist']
  },
  {
    id: 4,
    name: 'Carlos Andrade',
    role: ['Recepcionista'],
    email: '<EMAIL>',
    phone: '(11) 98888-0003',
    avatar: 'https://placehold.co/100x100.png',
    crm: '',
    admissionDate: '2023-08-01',
    salary: 3500,
    shift: 'integral',
    status: 'ativo',
    accessLevel: 'basico',
    address: 'Rua da Recepção, 101 - Portaria',
    specialty: 'Atendimento',
    permissions: ['receptionist']
  },
];


export const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'agenda', label: 'Agenda', icon: Calendar },
    { id: 'appointments', label: 'Consultas', icon: Activity },
    { id: 'patients', label: 'Pacientes', icon: Users },
    { id: 'insurances', label: 'Convênios', icon: Shield },
    { id: 'medical-records', label: 'Prontuários', icon: FileText },
    { id: 'staff', label: 'Profissionais', icon: UserCheck },
    { id: 'payments', label: 'Pagamentos', icon: DollarSign },
    { id: 'inventory', label: 'Estoque', icon: Archive },
    { id: 'reports', label: 'Relatórios', icon: TrendingUp },
    { id: 'notifications', label: 'Notificações', icon: Bell },
    { id: 'settings', label: 'Configurações', icon: Settings }
  ];

export const analytics = {
    totalPatients: 1247,
    activePatients: 892,
    totalAppointments: 23,
    completedAppointments: 18,
    pendingAppointments: 5,
    totalRevenue: 45680,
    monthlyRevenue: 12350,
    monthlyGrowth: {
      patients: 12,
      appointments: 8,
      revenue: 15,
    }
};

const today = new Date();
const todayFormatted = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
const tomorrow = new Date();
tomorrow.setDate(today.getDate() + 1);
const tomorrowFormatted = `${tomorrow.getFullYear()}-${String(tomorrow.getMonth() + 1).padStart(2, '0')}-${String(tomorrow.getDate()).padStart(2, '0')}`;


export const agendaEvents: AgendaEvent[] = [
    {
      id: 'appointment-1',
      title: 'Consulta: João Silva',
      date: todayFormatted,
      time: '09:00',
      duration: 30,
      type: 'consulta',
      status: 'agendado',
      description: 'Consulta com Dra. Maria Santos',
      location: 'Sala 101',
      attendees: ['Dra. Maria Santos', 'João Silva'],
      color: 'blue',
      priority: 'normal'
    },
    {
      id: 'appointment-2',
      title: 'Exame: Ana Oliveira',
      date: todayFormatted,
      time: '14:30',
      duration: 45,
      type: 'consulta',
      status: 'confirmado',
      description: 'Exame com Dr. Pedro Costa',
      location: 'Sala 203',
      attendees: ['Dr. Pedro Costa', 'Ana Oliveira'],
      color: 'blue',
      priority: 'normal'
    },
    {
      id: 'team-1',
      title: 'Reunião de Equipe Médica',
      date: tomorrowFormatted,
      time: '08:00',
      duration: 60,
      type: 'reuniao',
      status: 'agendado',
      description: 'Reunião semanal da equipe médica para discussão de casos',
      location: 'Sala de Reuniões',
      attendees: ['Dra. Maria Santos', 'Dr. Pedro Costa', 'Enf. Ana Silva'],
      color: 'green',
      priority: 'normal'
    },
     {
      id: 'appointment-3',
      title: 'Procedimento: Carlos Mendes',
      date: todayFormatted,
      time: '11:00',
      duration: 60,
      type: 'procedimento',
      status: 'realizado',
      description: 'Exames de rotina e avaliação colesterol',
      location: 'Sala 105',
      attendees: ['Dr. Ana Silva', 'Carlos Mendes'],
      color: 'orange',
      priority: 'alta'
    },
];

export const inventoryItems: InventoryItem[] = [
    { id: 1, name: 'Dipirona 500mg', category: 'Medicamento', stock: 150, minStock: 50, status: 'ok', unitCost: 0.50, supplier: 'MedPharma', expirationDate: '2025-12-31' },
    { id: 2, name: 'Seringas 5ml', category: 'Material Descartável', stock: 25, minStock: 100, status: 'low', unitCost: 0.80, supplier: 'HealthSupplies', expirationDate: null },
    { id: 3, name: 'Álcool 70%', category: 'Antisséptico', stock: 15, minStock: 30, status: 'low', unitCost: 15.00, supplier: 'CleanHealth', expirationDate: '2026-06-30' },
    { id: 4, name: 'Luvas Descartáveis (M)', category: 'Material Descartável', stock: 80, minStock: 200, status: 'low', unitCost: 0.25, supplier: 'HealthSupplies', expirationDate: null },
    { id: 5, name: 'Amoxicilina 500mg', category: 'Medicamento', stock: 50, minStock: 20, status: 'ok', unitCost: 1.20, supplier: 'MedPharma', expirationDate: '2025-08-31' }
];

export const payments: Payment[] = [
  { id: 1, patientName: 'João Silva', service: 'Consulta Cardiológica', amount: 180.00, method: 'PIX', date: '2024-05-15', status: 'pago' },
  { id: 2, patientName: 'Ana Oliveira', service: 'Hemograma Completo', amount: 45.00, method: 'Cartão de Crédito', date: '2024-05-14', status: 'pago' },
  { id: 3, patientName: 'Carlos Mendes', service: 'Consulta Clínica Geral', amount: 150.00, method: 'Convênio', date: '2024-05-20', status: 'pendente' }
];

export const insurances: Insurance[] = [
    { id: 1, name: 'Amil', cnpj: '29.309.127/0001-79', phone: '0800-706-2363', status: 'ativo' },
    { id: 2, name: 'Bradesco Saúde', cnpj: '92.693.118/0001-60', phone: '0800-727-9966', status: 'ativo' },
    { id: 3, name: 'Unimed', cnpj: '03.812.597/0001-85', phone: '0800-772-3030', status: 'ativo' },
    { id: 4, name: 'SulAmérica', cnpj: '01.685.053/0001-56', phone: '0800-722-0504', status: 'inativo' }
];

export const notifications: Notification[] = [
    { id: 1, type: 'inventory', title: 'Estoque Baixo', message: 'Seringas 5ml estão acabando.', timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), priority: 'high', read: false },
    { id: 2, type: 'payment', title: 'Pagamento Recebido', message: 'Carlos Mendes pagou R$ 180,00.', timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), priority: 'low', read: true }
];
    
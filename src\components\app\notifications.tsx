"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardT<PERSON>le, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>, Check, Settings, Trash2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import type { Notification } from '@/lib/types';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface NotificationsProps {
  notifications: Notification[];
  setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>;
}

export default function Notifications({ notifications, setNotifications }: NotificationsProps) {

  const markAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, read: true })));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getPriorityVariant = (priority: string) => {
    if (priority === 'high') return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
    if (priority === 'medium') return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
    return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
  };
  
  const getPriorityText = (priority: 'high' | 'medium' | 'low') => {
      const texts = { high: 'Alta', medium: 'Média', low: 'Baixa' };
      return texts[priority];
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Notificações</h1>
        <div className="flex items-center gap-2">
           <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="icon"><Settings className="h-4 w-4"/></Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Configurações de Notificação</DialogTitle>
                </DialogHeader>
                 <div className="space-y-4 py-4">
                    <div className="flex items-center justify-between rounded-lg border p-4">
                        <div>
                          <Label htmlFor="system-notifications">Notificações no Sistema</Label>
                          <p className="text-sm text-muted-foreground">Receba alertas dentro do ClinicFlow.</p>
                        </div>
                        <Switch id="system-notifications" defaultChecked/>
                    </div>
                     <div className="flex items-center justify-between rounded-lg border p-4">
                        <div>
                          <Label htmlFor="email-notifications">Notificações por Email</Label>
                          <p className="text-sm text-muted-foreground">Receba resumos e alertas importantes no seu email.</p>
                        </div>
                        <Switch id="email-notifications" defaultChecked/>
                    </div>
                 </div>
              </DialogContent>
           </Dialog>
           <Button variant="outline" onClick={markAllAsRead} disabled={notifications.every(n => n.read)}>
             <Check className="mr-2 h-4 w-4" /> Marcar como lidas
           </Button>
           <Button variant="destructive" onClick={clearAll} disabled={notifications.length === 0}>
             <Trash2 className="mr-2 h-4 w-4" /> Limpar Tudo
           </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Central de Notificações</CardTitle>
          <CardDescription>Gerencie e visualize todas as notificações do sistema.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-4">
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filtrar por tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Tipos</SelectItem>
                <SelectItem value="appointment">Consultas</SelectItem>
                <SelectItem value="inventory">Estoque</SelectItem>
                <SelectItem value="payment">Pagamentos</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filtrar por status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Status</SelectItem>
                <SelectItem value="read">Lidas</SelectItem>
                <SelectItem value="unread">Não Lidas</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-4">
            {notifications.length > 0 ? notifications.map(notification => (
              <div key={notification.id} className={`flex items-start gap-4 p-4 rounded-lg border transition-colors ${notification.read ? 'bg-card' : 'bg-primary/5 dark:bg-primary/10'}`}>
                <div className={`p-2 rounded-full ${notification.read ? 'bg-muted' : 'bg-primary/10'}`}>
                  <Bell className={`h-5 w-5 ${notification.read ? 'text-muted-foreground' : 'text-primary'}`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="font-semibold">{notification.title}</p>
                    <Badge variant="outline" className={getPriorityVariant(notification.priority)}>
                      {getPriorityText(notification.priority)}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{notification.message}</p>
                  <p className="text-xs text-muted-foreground mt-2">{formatDistanceToNow(notification.timestamp, { addSuffix: true, locale: ptBR })}</p>
                </div>
                {!notification.read && (
                    <div className="h-2 w-2 rounded-full bg-primary mt-1 animate-pulse"></div>
                )}
              </div>
            )) : (
              <div className="text-center py-12">
                <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">Tudo em ordem</h3>
                <p className="mt-1 text-sm text-muted-foreground">Você não tem novas notificações.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Users, DollarSign, Archive, Download } from 'lucide-react';
import { ExportReportModal } from './modals/export-report-modal';
import type { ReportType } from '@/lib/types';

export default function Reports() {
    const [isExportModalOpen, setExportModalOpen] = useState(false);
    const [selectedReport, setSelectedReport] = useState<ReportType | null>(null);

    const handleExportClick = (reportType: ReportType) => {
        setSelectedReport(reportType);
        setExportModalOpen(true);
    };

    const reports = [
        { 
            type: 'financial' as ReportType, 
            title: 'Relatório Financeiro', 
            description: 'Análise detalhada de receitas, despesas e pagamentos.',
            icon: DollarSign 
        },
        { 
            type: 'patients' as ReportType, 
            title: 'Relatório de Pacientes', 
            description: 'Dados demográficos, status e histórico dos pacientes.',
            icon: Users
        },
        { 
            type: 'appointments' as ReportType, 
            title: 'Relatório de Consultas', 
            description: 'Estatísticas sobre agendamentos, status e especialidades.',
            icon: FileText
        },
        { 
            type: 'inventory' as ReportType, 
            title: 'Relatório de Estoque', 
            description: 'Visão geral do inventário, itens com estoque baixo e validades.',
            icon: Archive
        }
    ];

  return (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Relatórios e Análises</h1>
        </div>

        <Card>
            <CardHeader>
                <CardTitle>Gerador de Relatórios</CardTitle>
                <CardDescription>Selecione um tipo de relatório para gerar e exportar os dados.</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    {reports.map((report) => (
                        <Card key={report.type} className="flex flex-col">
                            <CardHeader>
                                <div className="flex items-start gap-4">
                                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                                        <report.icon className="h-6 w-6 text-primary" />
                                    </div>
                                    <div>
                                        <CardTitle>{report.title}</CardTitle>
                                        <CardDescription className="mt-1">{report.description}</CardDescription>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="flex-grow"></CardContent>
                            <div className="p-6 pt-0">
                                <Button className="w-full" onClick={() => handleExportClick(report.type)}>
                                    <Download className="mr-2 h-4 w-4" />
                                    Gerar Relatório
                                </Button>
                            </div>
                        </Card>
                    ))}
                </div>
            </CardContent>
        </Card>

        {isExportModalOpen && selectedReport && (
            <ExportReportModal 
                reportType={selectedReport}
                onClose={() => setExportModalOpen(false)} 
            />
        )}
    </div>
  );
}

    
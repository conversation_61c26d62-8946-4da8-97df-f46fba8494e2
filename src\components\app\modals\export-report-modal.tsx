"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Download, FileText, FileSpreadsheet, FileArchive } from 'lucide-react';
import type { ReportType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ExportReportModalProps {
    reportType: ReportType;
    onClose: () => void;
}

export function ExportReportModal({ reportType, onClose }: ExportReportModalProps) {
    const [format, setFormat] = useState<'pdf' | 'csv' | 'excel'>('pdf');
    const [period, setPeriod] = useState('last_month');
    const { toast } = useToast();

    const reportTitles = {
        financial: 'Relatório Financeiro',
        patients: 'Relatório de Pacientes',
        appointments: 'Relatório de Consultas',
        inventory: 'Relatório de Estoque'
    };

    const handleExport = () => {
        toast({
            title: "Exportação Iniciada",
            description: `Seu ${reportTitles[reportType].toLowerCase()} está sendo gerado em formato ${format.toUpperCase()}.`,
        });
        onClose();
    };

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-lg">
                <DialogHeader>
                    <DialogTitle>Exportar {reportTitles[reportType]}</DialogTitle>
                    <DialogDescription>
                        Selecione o formato e o período para exportar o relatório.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4">
                    <div>
                        <Label className="mb-2 block">Formato do Arquivo</Label>
                        <div className="grid grid-cols-3 gap-2">
                            <Button variant={format === 'pdf' ? 'default' : 'outline'} onClick={() => setFormat('pdf')} className="h-auto py-2">
                                <FileText className="mb-1 h-5 w-5" />
                                PDF
                            </Button>
                            <Button variant={format === 'csv' ? 'default' : 'outline'} onClick={() => setFormat('csv')} className="h-auto py-2">
                                <FileArchive className="mb-1 h-5 w-5" />
                                CSV
                            </Button>
                             <Button variant={format === 'excel' ? 'default' : 'outline'} onClick={() => setFormat('excel')} className="h-auto py-2">
                                <FileSpreadsheet className="mb-1 h-5 w-5" />
                                Excel
                            </Button>
                        </div>
                    </div>
                     <div className="space-y-2">
                        <Label htmlFor="period">Período</Label>
                        <Select value={period} onValueChange={setPeriod}>
                            <SelectTrigger id="period">
                                <SelectValue placeholder="Selecione o período" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="last_week">Última Semana</SelectItem>
                                <SelectItem value="last_month">Último Mês</SelectItem>
                                <SelectItem value="last_quarter">Último Trimestre</SelectItem>
                                <SelectItem value="last_year">Último Ano</SelectItem>
                                <SelectItem value="all_time">Todo o Período</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button onClick={handleExport}>
                        <Download className="mr-2 h-4 w-4" />
                        Gerar e Baixar
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

    
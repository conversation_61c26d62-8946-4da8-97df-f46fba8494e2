"use client";

import React from 'react';
import { insurances } from '@/lib/data';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, Plus, Shield } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface InsurancesProps {
  onAddNew: () => void;
}

export default function Insurances({ onAddNew }: InsurancesProps) {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'ativo': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      case 'inativo': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
            <Shield className="h-8 w-8 text-primary" />
            <div>
                <h1 className="text-2xl font-bold">Gestão de Convênios</h1>
                <p className="text-muted-foreground">Adicione e gerencie os convênios aceitos na clínica.</p>
            </div>
        </div>
        <Button onClick={onAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Adicionar Convênio
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Convênios Cadastrados</CardTitle>
          <CardDescription>Lista de todos os convênios cadastrados no sistema.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome do Convênio</TableHead>
                <TableHead>CNPJ</TableHead>
                <TableHead>Telefone</TableHead>
                <TableHead>Status</TableHead>
                <TableHead><span className="sr-only">Ações</span></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {insurances.map((insurance) => (
                <TableRow key={insurance.id}>
                  <TableCell className="font-medium">{insurance.name}</TableCell>
                  <TableCell>{insurance.cnpj}</TableCell>
                  <TableCell>{insurance.phone}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusVariant(insurance.status)}>
                      {insurance.status.charAt(0).toUpperCase() + insurance.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                     <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button aria-haspopup="true" size="icon" variant="ghost">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Abrir menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>Editar</DropdownMenuItem>
                          <DropdownMenuItem>Ver Detalhes</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">Desativar</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

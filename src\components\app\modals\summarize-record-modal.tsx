"use client";

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { medicalRecordSummarizer } from '@/app/actions';
import { <PERSON><PERSON>les, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import type { Patient } from '@/lib/types';

interface SummarizeRecordModalProps {
  onClose: () => void;
  patient: Patient | null;
}

export function SummarizeRecordModal({ onClose, patient }: SummarizeRecordModalProps) {
  const [medicalRecord, setMedicalRecord] = useState('');
  const [summary, setSummary] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (patient) {
      setMedicalRecord(patient.medicalHistory);
    }
  }, [patient]);

  const handleSummarize = async () => {
    if (!medicalRecord.trim()) {
        setError("O prontuário não pode estar vazio.");
        return;
    }
    setIsLoading(true);
    setError(null);
    setSummary('');

    const result = await medicalRecordSummarizer({ medicalRecord });
    
    if (result.success) {
        setSummary(result.summary);
    } else {
        setError(result.error || "Falha ao gerar o resumo.");
    }

    setIsLoading(false);
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Resumo de Prontuário com IA</DialogTitle>
          <DialogDescription>
            {patient 
              ? `Gerando resumo para ${patient.name}.`
              : "Cole o prontuário abaixo para gerar um resumo conciso usando IA."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid w-full gap-1.5">
            <Label htmlFor="medical-record">Prontuário Médico</Label>
            <Textarea
              placeholder="Cole o texto do prontuário aqui ou selecione um paciente..."
              id="medical-record"
              value={medicalRecord}
              onChange={(e) => setMedicalRecord(e.target.value)}
              rows={10}
              disabled={isLoading || !!patient}
            />
          </div>
          {summary && (
            <div className="grid w-full gap-1.5">
                <Label htmlFor="summary">Resumo</Label>
                <div className="p-4 border rounded-md bg-muted text-sm whitespace-pre-wrap">
                    {summary}
                </div>
            </div>
          )}
          {error && (
             <Alert variant="destructive">
                <AlertTitle>Erro</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>Cancelar</Button>
          <Button onClick={handleSummarize} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Gerando...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Gerar Resumo
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

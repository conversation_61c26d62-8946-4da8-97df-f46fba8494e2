"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { patients, staff } from '@/lib/data';
import type { Appointment, Patient } from '@/lib/types';

interface AppointmentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (appointment: Omit<Appointment, 'id'>) => void;
    appointment: Appointment | null;
    patientForNewAppointment?: Patient | null;
}

export default function AppointmentModal({ isOpen, onClose, onSave, appointment, patientForNewAppointment }: AppointmentModalProps) {
    const [patientId, setPatientId] = useState('');
    const [doctorId, setDoctorId] = useState('');
    const [date, setDate] = useState('');
    const [time, setTime] = useState('');
    const [type, setType] = useState('Consulta');

    useEffect(() => {
        if (appointment) {
            const patient = patients.find(p => p.name === appointment.patientName);
            const doctor = staff.find(s => s.name === appointment.doctorName);
            setPatientId(patient ? String(patient.id) : '');
            setDoctorId(doctor ? String(doctor.id) : '');
            setDate(appointment.date);
            setTime(appointment.time);
            setType(appointment.type);
        } else if (patientForNewAppointment) {
            setPatientId(String(patientForNewAppointment.id));
            setDoctorId('');
            setDate('');
            setTime('');
            setType('Consulta');
        } else {
            // Reset form for new appointment
            setPatientId('');
            setDoctorId('');
            setDate('');
            setTime('');
            setType('Consulta');
        }
    }, [appointment, patientForNewAppointment, isOpen]);

    const handleSubmit = () => {
        const patient = patients.find(p => p.id === Number(patientId));
        const doctor = staff.find(s => s.id === Number(doctorId));

        if (patient && doctor && date && time) {
            const appointmentData: Omit<Appointment, 'id'> = {
                patientId: patient.id,
                patientName: patient.name,
                doctorName: doctor.name,
                date,
                time,
                duration: 30, // default duration
                type: type as Appointment['type'],
                specialty: doctor.specialty,
                status: 'agendado',
                notes: appointment?.notes || '',
                room: appointment?.room || 'Sala 101',
            };
            onSave(appointmentData);
        } else {
            // TODO: Add proper validation and error handling
            console.error("Form is incomplete");
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-lg">
                <DialogHeader>
                    <DialogTitle>{appointment ? 'Editar Agendamento' : 'Novo Agendamento'}</DialogTitle>
                    <DialogDescription>
                        {appointment ? 'Altere os detalhes da consulta.' : 'Agende uma nova consulta para um paciente.'}
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="patient">Paciente</Label>
                        <Select value={patientId} onValueChange={setPatientId} disabled={!!patientForNewAppointment}>
                            <SelectTrigger id="patient">
                                <SelectValue placeholder="Selecione um paciente" />
                            </SelectTrigger>
                            <SelectContent>
                                {patients.map(p => <SelectItem key={p.id} value={String(p.id)}>{p.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="doctor">Médico</Label>
                         <Select value={doctorId} onValueChange={setDoctorId}>
                            <SelectTrigger id="doctor">
                                <SelectValue placeholder="Selecione um médico" />
                            </SelectTrigger>
                            <SelectContent>
                                {staff.map(s => <SelectItem key={s.id} value={String(s.id)}>{s.name} - {s.specialty}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="date">Data</Label>
                            <Input id="date" type="date" value={date} onChange={e => setDate(e.target.value)} />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="time">Hora</Label>
                            <Input id="time" type="time" value={time} onChange={e => setTime(e.target.value)} />
                        </div>
                    </div>
                     <div className="space-y-2">
                        <Label htmlFor="type">Tipo</Label>
                         <Select value={type} onValueChange={(value) => setType(value as Appointment['type'])}>
                            <SelectTrigger id="type">
                                <SelectValue placeholder="Selecione o tipo de consulta" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="Consulta">Consulta</SelectItem>
                                <SelectItem value="Exame">Exame</SelectItem>
                                <SelectItem value="Procedimento">Procedimento</SelectItem>
                                <SelectItem value="Retorno">Retorno</SelectItem>
                                <SelectItem value="Urgência">Urgência</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button onClick={handleSubmit}>{appointment ? 'Salvar Alterações' : 'Agendar Consulta'}</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

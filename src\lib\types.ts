export type Patient = {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  birthDate: string;
  cpf: string;
  bloodType: string;
  allergies: string[];
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  lastVisit: string;
  status: 'ativo' | 'inativo' | 'pendente';
  medicalHistory: string;
  insurance: string;
  totalAppointments: number;
  profession: string;
  interests: string[];
  interactions: number;
  age: number;
  isBirthday: boolean;
  birthdayThisWeek: boolean;
  avatar: string;
  paymentType: 'particular' | 'convenio';
};

export type Appointment = {
  id: number;
  patientId: number;
  patientName: string;
  doctorName: string;
  date: string;
  time: string;
  duration: number;
  type: 'Consulta' | 'Exame' | 'Procedimento' | 'Retorno' | 'Urgência';
  specialty: string;
  status: 'agendado' | 'confirmado' | 'realizado' | 'cancelado' | 'pendente';
  notes: string;
  room: string;
};

export type Staff = {
  id: number;
  name: string;
  role: string[];
  email: string;
  phone: string;
  crm: string;
  admissionDate: string;
  salary: number;
  shift: 'manha' | 'tarde' | 'noite' | 'integral' | 'plantao';
  status: 'ativo' | 'ferias' | 'licenca' | 'inativo';
  accessLevel: 'basico' | 'intermediario' | 'avancado' | 'administrador';
  address: string;
  specialty: string;
  avatar: string;
  permissions: string[];
};

export type Notification = {
    id: number;
    type: 'appointment' | 'medication' | 'inventory' | 'payment' | 'schedule' | 'birthday' | 'system';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
    priority: 'low' | 'medium' | 'high';
};

export type AgendaEvent = {
  id: string;
  title: string;
  date: string;
  time: string;
  duration: number;
  type: 'consulta' | 'reuniao' | 'treinamento' | 'manutencao' | 'procedimento';
  status: string;
  description: string;
  location: string;
  attendees: string[];
  color: 'blue' | 'green' | 'purple' | 'orange' | 'yellow' | 'red';
  priority: 'alta' | 'media' | 'baixa' | 'normal';
};

export type Payment = {
  id: number;
  patientName: string;
  service: string;
  amount: number;
  method: 'PIX' | 'Cartão de Crédito' | 'Cartão de Débito' | 'Dinheiro' | 'Convênio';
  date: string;
  status: 'pago' | 'pendente' | 'cancelado';
};

export type ReportType = 'financial' | 'patients' | 'appointments' | 'inventory';

export type Insurance = {
  id: number;
  name: string;
  cnpj: string;
  phone: string;
  status: 'ativo' | 'inativo';
};

export type InventoryItem = {
  id: number;
  name: string;
  category: string;
  stock: number;
  minStock: number;
  status: 'ok' | 'low' | 'out';
  unitCost: number;
  supplier: string;
  expirationDate: string | null;
};
    
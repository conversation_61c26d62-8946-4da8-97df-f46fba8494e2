"use client";

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/app/app-layout';
import LoginPage from './login/page';
import type { Staff } from '@/lib/types';

export default function ClinicManagementApp() {
  const [currentUser, setCurrentUser] = useState<Staff | null>(null);

  // Check for auth state on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('clinicflow-user');
    if (storedUser) {
      try {
        setCurrentUser(JSON.parse(storedUser));
      } catch (error) {
        // Handle potential parsing errors
        localStorage.removeItem('clinicflow-user');
      }
    }
  }, []);

  const handleLogin = (user: Staff) => {
    localStorage.setItem('clinicflow-user', JSON.stringify(user));
    setCurrentUser(user);
  };
  
  const handleLogout = () => {
    localStorage.removeItem('clinicflow-user');
    setCurrentUser(null);
  };

  if (!currentUser) {
    return <LoginPage onLogin={handleLogin} />;
  }

  return <AppLayout user={currentUser} onLogout={handleLogout} />;
}

"use client";

import React from 'react';
import type { Staff } from '@/lib/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Mail, Phone, Settings, UserPlus, Edit } from 'lucide-react';
import { Badge } from '../ui/badge';

interface StaffProps {
    staff: Staff[];
    onAddNew: () => void;
    onEdit: (staffMember: Staff) => void;
}

export default function Staff({ staff, onAddNew, onEdit }: StaffProps) {
  return (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Gestão de Profissionais</h1>
            <Button onClick={onAddNew}>
                <UserPlus className="mr-2 h-4 w-4" />
                Adicionar Profissional
            </Button>
        </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {staff.map((member) => (
          <Card key={member.id} className="flex flex-col">
            <CardHeader className="flex flex-row items-start gap-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={member.avatar} data-ai-hint="professional portrait" />
                <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <CardTitle>{member.name}</CardTitle>
                 <div className="flex flex-wrap gap-1 mt-2">
                    {member.role.map((role, index) => (
                      <Badge key={index} variant="secondary">{role}</Badge>
                    ))}
                  </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-2 flex-grow">
              <div className="flex items-center text-sm text-muted-foreground">
                <Mail className="mr-2 h-4 w-4" />
                <span>{member.email}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Phone className="mr-2 h-4 w-4" />
                <span>{member.phone}</span>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between items-center">
              <Badge variant={member.status === 'ativo' ? 'default' : 'outline'} className={member.status === 'ativo' ? 'bg-green-100 text-green-800' : ''}>
                {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
              </Badge>
              <div className="flex gap-1">
                <Button variant="ghost" size="icon" onClick={() => onEdit(member)}>
                  <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" disabled>
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

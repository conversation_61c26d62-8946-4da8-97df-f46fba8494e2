"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { KeyRound, LogOut, Save, Calendar, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import type { Staff } from '@/lib/types';

// Simple WhatsApp Icon as an inline SVG component
const WhatsAppIcon = ({ className }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={className}>
        <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.894 11.892-1.99 0-3.903-.52-5.58-1.455l-6.323 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.433-9.89-9.889-9.89-5.452 0-9.887 4.428-9.888 9.89 .001 2.228.651 4.39 1.899 6.27l-1.2 4.419 4.555-1.196z"/>
    </svg>
);


interface SettingsProps {
  user: Staff;
  onLogout: () => void;
  darkMode: boolean;
  setDarkMode: (dark: boolean) => void;
}

export default function Settings({ user, onLogout, darkMode, setDarkMode }: SettingsProps) {
  const [apiKey, setApiKey] = useState('');
  const [whatsappConnected, setWhatsappConnected] = useState(false);
  const [googleCalendarConnected, setGoogleCalendarConnected] = useState(false);
  const { toast } = useToast();

  const handleSaveChanges = (section: string) => {
     toast({
      title: "Alterações Salvas",
      description: `As configurações de ${section} foram salvas com sucesso.`,
    });
  }

  const handleIntegrationConnect = (integration: 'whatsapp' | 'google-calendar') => {
    if (integration === 'whatsapp') {
        setWhatsappConnected(true);
    } else if (integration === 'google-calendar') {
        setGoogleCalendarConnected(true);
    }
    toast({
      title: "Integração Conectada",
      description: `Conexão com ${integration === 'whatsapp' ? 'WhatsApp Business' : 'Google Calendar'} estabelecida.`,
    });
  };

  return (
    <div className="space-y-6">
       <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Configurações</h1>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Perfil</TabsTrigger>
          <TabsTrigger value="clinic">Clínica</TabsTrigger>
          <TabsTrigger value="security">Segurança</TabsTrigger>
          <TabsTrigger value="integrations">Integrações</TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Perfil do Usuário</CardTitle>
              <CardDescription>Gerencie suas informações pessoais.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                        <Label htmlFor="name">Nome</Label>
                        <Input id="name" defaultValue={user.name} />
                    </div>
                    <div className="space-y-1">
                        <Label htmlFor="role">Cargo</Label>
                        <Input id="role" defaultValue={user.role.join(', ')} readOnly />
                    </div>
                </div>
                 <div className="space-y-1">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" defaultValue={user.email} type="email"/>
                </div>
                 <Button onClick={() => handleSaveChanges('perfil')}>
                   <Save className="mr-2 h-4 w-4" />
                   Salvar Alterações
                  </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="clinic">
           <Card>
            <CardHeader>
              <CardTitle>Configurações da Clínica</CardTitle>
              <CardDescription>Gerencie as informações gerais da clínica.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                 <div className="space-y-1">
                    <Label htmlFor="clinicName">Nome da Clínica</Label>
                    <Input id="clinicName" defaultValue="ClinicFlow" />
                </div>
                 <div className="space-y-1">
                    <Label htmlFor="address">Endereço</Label>
                    <Input id="address" defaultValue="123 Health St, Medcity" />
                </div>
                 <Button onClick={() => handleSaveChanges('clínica')}>
                  <Save className="mr-2 h-4 w-4" />
                  Salvar Alterações
                  </Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="security">
           <Card>
            <CardHeader>
              <CardTitle>Segurança e Aparência</CardTitle>
              <CardDescription>Gerencie suas configurações de segurança e tema.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                        <Label htmlFor="two-factor" className="flex items-center gap-2">Autenticação de Dois Fatores</Label>
                        <p className="text-sm text-muted-foreground">Adicione uma camada extra de segurança à sua conta.</p>
                    </div>
                    <Switch id="two-factor" />
                </div>
                 <div className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                        <Label htmlFor="dark-mode">Modo Escuro</Label>
                        <p className="text-sm text-muted-foreground">Alterne o tema escuro para a aplicação.</p>
                    </div>
                    <Switch id="dark-mode" checked={darkMode} onCheckedChange={setDarkMode} />
                </div>
                <div className="space-y-2 pt-2">
                    <Button>Alterar Senha</Button>
                </div>
                <Card>
                  <CardHeader>
                    <CardTitle>Sair de outras sessões</CardTitle>
                    <CardDescription>Para sua segurança, você pode sair de todas as outras sessões ativas em outros dispositivos.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="destructive" onClick={onLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Sair de todos os dispositivos
                    </Button>
                  </CardContent>
                </Card>
            </CardContent>
          </Card>
        </TabsContent>
         <TabsContent value="integrations">
           <Card>
            <CardHeader>
              <CardTitle>Integrações e API</CardTitle>
              <CardDescription>Conecte o ClinicFlow a outros serviços e configure chaves de API.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="rounded-lg border p-4">
                  <div className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                        <KeyRound className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <Label className="text-base" htmlFor="api-key">Google AI</Label>
                        <p className="text-sm text-muted-foreground">Insira sua chave de API do Gemini para habilitar as funcionalidades de IA.</p>
                    </div>
                  </div>
                   <div className="mt-4 space-y-2">
                      <Input 
                        id="api-key" 
                        type="password"
                        placeholder="Cole sua GEMINI_API_KEY aqui" 
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                      />
                      <Button onClick={() => handleSaveChanges('API')}>
                        <Save className="mr-2 h-4 w-4" />
                        Salvar Chave
                      </Button>
                   </div>
                </div>

                <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center gap-4">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                            <WhatsAppIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <Label>WhatsApp Business</Label>
                            <p className="text-sm text-muted-foreground">Envie lembretes e notificações automatizadas.</p>
                        </div>
                    </div>
                    <Button 
                        variant={whatsappConnected ? 'default' : 'outline'}
                        onClick={() => handleIntegrationConnect('whatsapp')}
                        disabled={whatsappConnected}
                        className={cn(whatsappConnected && 'bg-green-600 hover:bg-green-700')}
                    >
                         {whatsappConnected ? <CheckCircle className="mr-2 h-4 w-4" /> : null}
                        {whatsappConnected ? 'Conectado' : 'Conectar'}
                    </Button>
                </div>
                 <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center gap-4">
                         <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-sky-100 dark:bg-sky-900/30">
                            <Calendar className="h-6 w-6 text-sky-600 dark:text-sky-400" />
                        </div>
                        <div>
                            <Label>Google Calendar</Label>
                            <p className="text-sm text-muted-foreground">Sincronize seus agendamentos com o Google Agenda.</p>
                        </div>
                    </div>
                     <Button 
                        variant={googleCalendarConnected ? 'default' : 'outline'}
                        onClick={() => handleIntegrationConnect('google-calendar')}
                        disabled={googleCalendarConnected}
                        className={cn(googleCalendarConnected && 'bg-green-600 hover:bg-green-700')}
                    >
                         {googleCalendarConnected ? <CheckCircle className="mr-2 h-4 w-4" /> : null}
                        {googleCalendarConnected ? 'Conectado' : 'Conectar'}
                    </Button>
                </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

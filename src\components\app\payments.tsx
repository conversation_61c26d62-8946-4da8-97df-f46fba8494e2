"use client";

import React from 'react';
import { StatCard } from './stat-card';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { DollarSign, Clock, CheckCircle, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import type { Payment } from '@/lib/types';

interface PaymentsProps {
  payments: Payment[];
  onAddNew: () => void;
}

export default function Payments({ payments, onAddNew }: PaymentsProps) {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pago': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      case 'pendente': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'cancelado': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Pagamentos</h1>
        <Button onClick={onAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Registrar Pagamento
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <StatCard title="Receita (Mês)" value="R$ 12.350" icon={DollarSign} color="green" />
        <StatCard title="Pagamentos Pendentes" value="R$ 2.450" icon={Clock} color="yellow" />
        <StatCard title="Faturas Pagas" value="18" icon={CheckCircle} color="blue" />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transações Recentes</CardTitle>
          <CardDescription>Uma lista dos pagamentos recentes.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Paciente</TableHead>
                <TableHead>Serviço</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Método</TableHead>
                <TableHead>Data</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">{payment.patientName}</TableCell>
                  <TableCell>{payment.service}</TableCell>
                  <TableCell>R$ {payment.amount.toFixed(2)}</TableCell>
                  <TableCell>{payment.method}</TableCell>
                  <TableCell>{payment.date}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusVariant(payment.status)}>
                      {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

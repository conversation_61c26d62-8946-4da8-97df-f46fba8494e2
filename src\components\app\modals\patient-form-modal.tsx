"use client";

import { useState, useEffect } from "react";
import type { Patient } from "@/lib/types";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, User, Loader2 } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { insurances } from "@/lib/data";

interface PatientFormModalProps {
    patient: Patient | null;
    onClose: () => void;
}

export default function PatientFormModal({ patient, onClose }: PatientFormModalProps) {
    const isEditing = !!patient;
    const [paymentType, setPaymentType] = useState(patient?.paymentType || 'particular');
    const [cep, setCep] = useState('');
    const [logradouro, setLogradouro] = useState('');
    const [numero, setNumero] = useState('');
    const [bairro, setBairro] = useState('');
    const [cidade, setCidade] = useState('');
    const [uf, setUf] = useState('');
    const [isLoadingCep, setIsLoadingCep] = useState(false);

    useEffect(() => {
        if (patient && patient.address) {
            const parts = patient.address.split(',').map(p => p.trim());
            if (parts.length >= 3) {
                setLogradouro(parts[0] || '');
                setNumero(parts[1] || '');
                const bairroCidadeUf = parts[2].split('-').map(p => p.trim());
                setBairro(bairroCidadeUf[0] || '');
                if (bairroCidadeUf.length > 1) {
                    const cidadeUf = bairroCidadeUf[1].split(' ').map(p => p.trim());
                    setCidade(cidadeUf[0] || '');
                    setUf(cidadeUf.slice(1).join(' ') || '');
                }
            }
        }
    }, [patient]);

    const handleSave = () => {
        const fullAddress = `${logradouro}, ${numero}, ${bairro}, ${cidade} - ${uf}`.trim();
        // Aqui você chamaria a função para salvar os dados do paciente
        // com o endereço completo em `fullAddress`.
        console.log("Salvando paciente com endereço:", fullAddress);
        onClose(); // Fechar o modal após salvar
    };

    async function handleSearchCEP(cepValue: string) {
        const cepFormatted = cepValue.replace(/\D/g, '');
        setCep(cepFormatted);

        if (cepFormatted.length !== 8) {
            return;
        }

        setIsLoadingCep(true);
        try {
            const response = await fetch(`https://viacep.com.br/ws/${cepFormatted}/json/`);
            const data = await response.json();
            if (!data.erro) {
                setLogradouro(data.logradouro);
                setBairro(data.bairro);
                setCidade(data.localidade);
                setUf(data.uf);
            } else {
                alert("CEP não encontrado.");
            }
        } catch (error) {
            console.error("Erro ao buscar CEP:", error);
            alert("Erro ao buscar CEP. Tente novamente.");
        } finally {
            setIsLoadingCep(false);
        }
    }

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-2xl lg:max-w-3xl max-h-[90vh] flex flex-col p-0">
                <div className="p-6 pb-0">
                <DialogHeader className="flex-shrink-0">
                    <DialogTitle>{isEditing ? 'Editar Paciente' : 'Novo Paciente'}</DialogTitle>
                    <DialogDescription>
                        {isEditing ? `Editando informações para ${patient?.name}.` : 'Preencha os detalhes para adicionar um novo paciente.'}
                    </DialogDescription>
                </DialogHeader>
                </div>
                <ScrollArea className="flex-1 max-h-[60vh] px-6">
                    <Tabs defaultValue="personal" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="personal" className="text-xs sm:text-sm">Pessoais</TabsTrigger>
                            <TabsTrigger value="address" className="text-xs sm:text-sm">Endereço</TabsTrigger>
                            <TabsTrigger value="payment" className="text-xs sm:text-sm">Pagamento</TabsTrigger>
                        </TabsList>

                        <TabsContent value="personal" className="space-y-4 mt-4">
                            <div className="flex flex-col items-center gap-4 mb-6">
                                <div className="relative">
                                    <Avatar className="h-24 w-24">
                                        <AvatarImage src={patient?.avatar} data-ai-hint="patient photo" />
                                        <AvatarFallback className="text-2xl">
                                            <User />
                                        </AvatarFallback>
                                    </Avatar>
                                    <Button size="icon" variant="outline" className="absolute bottom-0 right-0 rounded-full h-6 w-6">
                                        <Camera className="h-3 w-3"/>
                                        <Input type="file" className="absolute inset-0 opacity-0 cursor-pointer"/>
                                    </Button>
                                </div>
                                <p className="text-xs text-muted-foreground text-center">Clique no ícone para alterar a foto</p>
                            </div>
                            <div className="grid gap-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Nome Completo</Label>
                                        <Input id="name" defaultValue={patient?.name} />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="cpf">CPF</Label>
                                        <Input id="cpf" defaultValue={patient?.cpf} />
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="email">Email</Label>
                                        <Input id="email" type="email" defaultValue={patient?.email} />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="phone">Telefone</Label>
                                        <Input id="phone" type="tel" defaultValue={patient?.phone} />
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="birthDate">Data de Nascimento</Label>
                                        <Input id="birthDate" type="date" defaultValue={patient?.birthDate} />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="bloodType">Tipo Sanguíneo</Label>
                                        <Input id="bloodType" defaultValue={patient?.bloodType} />
                                    </div>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="address" className="space-y-4 mt-4">
                            <div className="relative space-y-2">
                                <Label htmlFor="cep-address">CEP</Label>
                                <Input
                                    id="cep-address"
                                    value={cep}
                                    onChange={(e) => handleSearchCEP(e.target.value)}
                                    maxLength={9}
                                    placeholder="00000-000"
                                />
                                {isLoadingCep && <Loader2 className="absolute right-2 top-8 h-4 w-4 animate-spin" />}
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                                <div className="space-y-2 col-span-2">
                                    <Label htmlFor="logradouro-address">Logradouro</Label>
                                    <Input id="logradouro-address" value={logradouro} onChange={e => setLogradouro(e.target.value)} />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="number-address">Número</Label>
                                    <Input id="number-address" value={numero} onChange={e => setNumero(e.target.value)} />
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="bairro-address">Bairro</Label>
                                    <Input id="bairro-address" value={bairro} onChange={e => setBairro(e.target.value)} />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="cidade-address">Cidade</Label>
                                    <Input id="cidade-address" value={cidade} onChange={e => setCidade(e.target.value)} />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="uf-address">Estado (UF)</Label>
                                <Input id="uf-address" value={uf} onChange={e => setUf(e.target.value)} maxLength={2} />
                            </div>
                        </TabsContent>

                        <TabsContent value="payment" className="space-y-4 mt-4">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label>Tipo de Atendimento</Label>
                                    <RadioGroup defaultValue={paymentType} onValueChange={(value) => setPaymentType(value as "particular" | "convenio")} className="flex gap-4">
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="particular" id="particular" />
                                            <Label htmlFor="particular">Particular</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="convenio" id="convenio" />
                                            <Label htmlFor="convenio">Convênio</Label>
                                        </div>
                                    </RadioGroup>
                                </div>
                                {paymentType === 'convenio' && (
                                    <div className="grid gap-4 border p-4 rounded-md bg-muted/50">
                                        <div className="space-y-2">
                                            <Label htmlFor="insurance">Convênio</Label>
                                            <Select defaultValue={patient?.insurance}>
                                                <SelectTrigger id="insurance">
                                                    <SelectValue placeholder="Selecione o convênio" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {insurances.map(i => <SelectItem key={i.id} value={i.name}>{i.name}</SelectItem>)}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="insurance-plan">Tipo de Plano</Label>
                                            <Input id="insurance-plan" placeholder="Ex: Básico, Plus, Premium" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="insurance-card">Número do Cartão</Label>
                                            <Input id="insurance-card" placeholder="0000 0000 0000 0000" />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </TabsContent>
                    </Tabs>
                </ScrollArea>
                <div className="p-6 pt-4">
                    <DialogFooter className="flex-shrink-0">
                        <Button variant="outline" onClick={onClose}>Cancelar</Button>
                        <Button type="button" onClick={handleSave}>{isEditing ? 'Salvar Alterações' : 'Adicionar Paciente'}</Button>
                    </DialogFooter>
                </div>
            </DialogContent>
        </Dialog>
    )
}

"use client";

import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Patient } from '@/lib/types';
import { Cake, Phone, Mail } from 'lucide-react';

interface PatientCardProps {
  patient: Patient;
  onSelectPatient: (patient: Patient) => void;
}

export default function PatientCard({ patient, onSelectPatient }: PatientCardProps) {
  return (
    <Card onClick={() => onSelectPatient(patient)} className="cursor-pointer hover:shadow-lg transition-shadow">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{patient.name}</span>
          {patient.isBirthday && <Cake className="h-5 w-5 text-yellow-500 animate-pulse" />}
        </CardTitle>
        <CardDescription>{patient.profession}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex items-center text-sm text-muted-foreground">
            <Mail className="mr-2 h-4 w-4" />
            <span>{patient.email}</span>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
            <Phone className="mr-2 h-4 w-4" />
            <span>{patient.phone}</span>
        </div>
      </CardContent>
      <CardFooter>
        <Badge variant={patient.status === 'ativo' ? 'default' : 'outline'} className={patient.status === 'ativo' ? 'bg-green-100 text-green-800' : ''}>
          {patient.status}
        </Badge>
      </CardFooter>
    </Card>
  );
}

"use client";

import React, { useState } from 'react';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { AgendaEvent } from '@/lib/types';
import { addDays, eachDayOfInterval, endOfMonth, endOfWeek, format, startOfMonth, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface AgendaProps {
  onAddNewAppointment: () => void;
  onSelectEvent: (event: AgendaEvent) => void;
  events: AgendaEvent[];
}

export default function Agenda({ onAddNewAppointment, onSelectEvent, events }: AgendaProps) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [view, setView] = useState('month'); // month, week, day

  const getEventColor = (event: AgendaEvent) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-800',
      green: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/50 dark:text-green-300 dark:border-green-800',
      purple: 'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900/50 dark:text-purple-300 dark:border-purple-800',
      orange: 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/50 dark:text-orange-300 dark:border-orange-800',
    };
    return colors[event.color as keyof typeof colors] || colors.blue;
  };

  const renderMonthCalendar = () => {
    const firstDay = startOfMonth(selectedDate);
    const lastDay = endOfMonth(selectedDate);
    const daysInMonth = eachDayOfInterval({ start: startOfWeek(firstDay), end: endOfWeek(lastDay) });
    const today = new Date();

    return (
      <div className="grid grid-cols-7">
        {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground border-b border-r">{day}</div>
        ))}
        {daysInMonth.map((day, index) => {
          const isToday = format(day, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
          const isCurrentMonth = day.getMonth() === selectedDate.getMonth();
          const dayEvents = events.filter(event => event.date === format(day, 'yyyy-MM-dd'));

          return (
            <div key={index} className={cn('h-32 border-r border-b p-1 overflow-hidden relative', { 'bg-blue-50 dark:bg-blue-900/20': isToday, 'text-muted-foreground': !isCurrentMonth })}>
              <div className={cn('text-sm mb-1', { 'text-blue-600 dark:text-blue-300 font-bold': isToday })}>{format(day, 'd')}</div>
              <div className="space-y-1">
                {dayEvents.slice(0, 2).map(event => (
                  <div key={event.id} onClick={() => onSelectEvent(event)} className={cn('text-xs p-1 rounded cursor-pointer truncate border', getEventColor(event))} title={event.title}>
                    {event.time} - {event.title}
                  </div>
                ))}
                {dayEvents.length > 2 && <div className="text-xs text-muted-foreground font-medium">+{dayEvents.length - 2} mais</div>}
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  
  const renderWeekCalendar = () => {
    const start = startOfWeek(selectedDate);
    const end = endOfWeek(selectedDate);
    const days = eachDayOfInterval({ start, end });
    const today = new Date();
    
    return (
      <div className="grid grid-cols-7 border-t border-l">
        {days.map((day, dayIndex) => {
          const isToday = format(day, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
          const dayEvents = events.filter(event => event.date === format(day, 'yyyy-MM-dd'));
          return (
            <div key={dayIndex} className="border-b border-r">
              <div className={cn("text-center p-2 border-b", { 'bg-blue-100 dark:bg-blue-900/30': isToday })}>
                <p className="text-sm">{format(day, 'EEE', { locale: ptBR })}</p>
                <p className={cn("text-2xl font-bold", { "text-blue-600 dark:text-blue-300": isToday })}>{format(day, 'd')}</p>
              </div>
              <div className="p-2 space-y-2 min-h-[400px]">
                {dayEvents.map(event => (
                   <div key={event.id} onClick={() => onSelectEvent(event)} className={cn('text-xs p-2 rounded cursor-pointer border', getEventColor(event))}>
                     <p className="font-bold">{event.time} - {event.title}</p>
                     <p className="truncate">{event.description}</p>
                   </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>
    );
  };
  
  const renderDayCalendar = () => {
    const hours = Array.from({ length: 14 }, (_, i) => i + 7); // 7am to 8pm
    const dayEvents = events.filter(event => event.date === format(selectedDate, 'yyyy-MM-dd'));

    return (
      <div className="border-t">
        {hours.map(hour => (
          <div key={hour} className="flex border-b">
            <div className="w-20 text-center p-2 border-r text-sm text-muted-foreground">
              {`${hour}:00`}
            </div>
            <div className="flex-1 p-2 relative">
              {dayEvents
                .filter(event => parseInt(event.time.split(':')[0]) === hour)
                .map(event => {
                   const topPosition = (parseInt(event.time.split(':')[1]) / 60) * 100; // Calculate position based on minutes
                   return (
                     <div key={event.id} onClick={() => onSelectEvent(event)} className={cn('text-xs p-2 rounded cursor-pointer border absolute w-[calc(100%-1rem)]', getEventColor(event))} style={{ top: `${topPosition}%` }}>
                       <p className="font-bold">{event.time} - {event.title}</p>
                       <p>{event.description}</p>
                     </div>
                   );
                })
              }
            </div>
          </div>
        ))}
      </div>
    );
  }

  const changeDate = (amount: number) => {
    const newDate = new Date(selectedDate);
    if (view === 'month') newDate.setMonth(newDate.getMonth() + amount);
    else if (view === 'week') newDate.setDate(newDate.getDate() + (amount * 7));
    else newDate.setDate(newDate.getDate() + amount);
    setSelectedDate(newDate);
  };
  
  const getHeaderText = () => {
    if (view === 'month') return format(selectedDate, 'MMMM yyyy', { locale: ptBR });
    if (view === 'week') {
      const start = startOfWeek(selectedDate);
      const end = endOfWeek(selectedDate);
      return `${format(start, 'd MMM')} - ${format(end, 'd MMM yyyy', { locale: ptBR })}`;
    }
    return format(selectedDate, 'd MMMM yyyy', { locale: ptBR });
  }

  return (
    <div className="space-y-6">
       <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Agenda</h1>
        <Button onClick={onAddNewAppointment}>
          <Plus className="mr-2 h-4 w-4" />
          Novo Agendamento
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="icon" onClick={() => changeDate(-1)}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold capitalize">
                {getHeaderText()}
              </h2>
              <Button variant="outline" size="icon" onClick={() => changeDate(1)}>
                <ChevronRight className="h-4 w-4" />
              </Button>
               <Button variant="outline" onClick={() => setSelectedDate(new Date())}>Hoje</Button>
            </div>
            <div className="flex items-center gap-2">
                <Button variant={view === 'month' ? 'default' : 'outline'} onClick={() => setView('month')}>Mês</Button>
                <Button variant={view === 'week' ? 'default' : 'outline'} onClick={() => setView('week')}>Semana</Button>
                <Button variant={view === 'day' ? 'default' : 'outline'} onClick={() => setView('day')}>Dia</Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="border-t p-0">
          {view === 'month' && renderMonthCalendar()}
          {view === 'week' && renderWeekCalendar()}
          {view === 'day' && renderDayCalendar()}
        </CardContent>
      </Card>
    </div>
  );
}

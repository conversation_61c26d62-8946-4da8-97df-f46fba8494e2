"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import type { Staff } from '@/lib/types';

const professionalPermissions = ['professional', 'admin'];

export default function AttendanceLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    // In a real app, you'd get the user from an auth context
    const storedUser = localStorage.getItem('clinicflow-user');
    if (!storedUser) {
        router.replace('/');
        return;
    }
    
    try {
        const currentUser: Staff = JSON.parse(storedUser);
        const hasPermission = professionalPermissions.some(p => currentUser.permissions.includes(p));

        if (!hasPermission) {
          router.replace('/'); // Redirect to a safe page if not authorized
        } else {
          setIsAuthorized(true);
        }
    } catch(e) {
        router.replace('/');
    }
  }, [router]);

  useEffect(() => {
    // Apply dark mode if it's set in localStorage
    const isDark = localStorage.getItem('clinicflow-darkmode') === 'true';
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, []);

  if (!isAuthorized) {
    return (
        <div className="flex h-screen w-full items-center justify-center bg-background">
            <div className="flex flex-col items-center gap-4">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-muted-foreground">Verificando permissões...</p>
            </div>
        </div>
    );
  }

  return <>{children}</>;
}

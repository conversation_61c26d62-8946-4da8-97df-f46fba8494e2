"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { Staff } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { Plus, X } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

interface StaffModalProps {
    staff: Staff | null;
    onClose: () => void;
}

const ALL_PERMISSIONS = [
    { id: 'receptionist', label: 'Acesso Recepção (Agenda, Pacientes, Consultas)' },
    { id: 'professional', label: 'Acesso Profissional (Prontuários Médicos)' },
    { id: 'admin', label: 'Acesso Administrador (Total, incluindo finanças, estoque e gestão de usuários)' }
];

export default function StaffModal({ staff, onClose }: StaffModalProps) {
    const isEditing = !!staff;
    const [roles, setRoles] = useState<string[]>([]);
    const [currentRole, setCurrentRole] = useState('');
    const [permissions, setPermissions] = useState<string[]>([]);

    useEffect(() => {
        if (staff) {
            setRoles(staff.role);
            setPermissions(staff.permissions);
        } else {
            setRoles([]);
            setPermissions(['receptionist']); // Default for new user
        }
    }, [staff]);

    const handleAddRole = () => {
        if (currentRole.trim() && !roles.includes(currentRole.trim())) {
            setRoles([...roles, currentRole.trim()]);
            setCurrentRole('');
        }
    };

    const handleRemoveRole = (roleToRemove: string) => {
        setRoles(roles.filter(role => role !== roleToRemove));
    };

    const handlePermissionChange = (permissionId: string, checked: boolean) => {
        setPermissions(prev => 
            checked ? [...prev, permissionId] : prev.filter(p => p !== permissionId)
        );
    };
    

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-2xl">
                <DialogHeader>
                    <DialogTitle>{isEditing ? 'Editar Profissional' : 'Adicionar Profissional'}</DialogTitle>
                    <DialogDescription>
                        {isEditing ? 'Altere as informações e permissões do profissional.' : 'Preencha os detalhes para o novo membro da equipe.'}
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4">
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="staff-name">Nome Completo</Label>
                            <Input id="staff-name" defaultValue={staff?.name} placeholder="João da Silva" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="staff-email">Email</Label>
                            <Input id="staff-email" type="email" defaultValue={staff?.email} placeholder="<EMAIL>" />
                        </div>
                    </div>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                         <div className="space-y-2">
                            <Label htmlFor="staff-phone">Telefone</Label>
                            <Input id="staff-phone" type="tel" defaultValue={staff?.phone} placeholder="(11) 99999-9999" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="staff-role">Cargo(s)/Especialidade(s)</Label>
                            <div className="flex gap-2">
                                <Input 
                                    id="staff-role" 
                                    value={currentRole}
                                    onChange={(e) => setCurrentRole(e.target.value)}
                                    placeholder="ex: Cardiologista, Recepção" 
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault();
                                            handleAddRole();
                                        }
                                    }}
                                />
                                <Button type="button" size="icon" variant="outline" onClick={handleAddRole}>
                                    <Plus className="h-4 w-4" />
                                </Button>
                            </div>
                             <div className="flex flex-wrap gap-1 mt-2 min-h-[24px]">
                                {roles.map((role, index) => (
                                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                        {role}
                                        <button onClick={() => handleRemoveRole(role)} className="rounded-full hover:bg-muted-foreground/20">
                                            <X className="h-3 w-3" />
                                        </button>
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    </div>
                    
                    <div className="space-y-2 pt-2">
                        <Label>Permissões de Acesso</Label>
                        <div className="space-y-2 rounded-md border p-4">
                            {ALL_PERMISSIONS.map(permission => (
                                <div key={permission.id} className="flex flex-row items-start space-x-3">
                                    <Checkbox 
                                        id={`perm-${permission.id}`}
                                        checked={permissions.includes(permission.id)}
                                        onCheckedChange={(checked) => handlePermissionChange(permission.id, !!checked)}
                                    />
                                     <div className="grid gap-1.5 leading-none">
                                        <Label htmlFor={`perm-${permission.id}`} className="font-medium">
                                             {permission.label.split('(')[0]}
                                        </Label>
                                        <p className="text-xs text-muted-foreground">
                                           ({permission.label.split('(')[1] || ''}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                         <p className="text-xs text-muted-foreground">Define quais seções do sistema este usuário poderá acessar.</p>
                    </div>

                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button type="submit">{isEditing ? 'Salvar Alterações' : 'Adicionar Profissional'}</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

"use server";
import { medicalRecordSummarizer as summarize } from '@/ai/flows/medical-record-summarizer';
import type { MedicalRecordSummarizerInput } from '@/ai/flows/medical-record-summarizer';

export async function medicalRecordSummarizer(input: MedicalRecordSummarizerInput) {
  try {
    const result = await summarize(input);
    return { success: true, summary: result.summary };
  } catch (error) {
    console.error("Error in medicalRecordSummarizer action:", error);
    if (error instanceof Error) {
      return { success: false, error: error.message };
    }
    return { success: false, error: "An unknown error occurred." };
  }
}

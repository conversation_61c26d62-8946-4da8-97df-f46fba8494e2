"use client";

import React from 'react';
import { StatCard } from './stat-card';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Archive, AlertTriangle, Clock, DollarSign, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import type { InventoryItem } from '@/lib/types';

interface InventoryProps {
  inventory: InventoryItem[];
  onAddNew: () => void;
}

export default function Inventory({ inventory, onAddNew }: InventoryProps) {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'ok': return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300';
      case 'low': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
      case 'out': return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300';
      default: return 'secondary';
    }
  };
  
  const getStatusText = (status: 'ok' | 'low' | 'out') => {
    const texts = {
        ok: 'Em Estoque',
        low: 'Estoque Baixo',
        out: 'Sem Estoque'
    };
    return texts[status];
  }

  const totalValue = inventory.reduce((acc, item) => acc + (item.stock * item.unitCost), 0);
  const lowStockItems = inventory.filter(item => item.status === 'low').length;
  const soonToExpireItems = inventory.filter(item => {
    if (!item.expirationDate) return false;
    const today = new Date();
    const expiration = new Date(item.expirationDate);
    const diffTime = expiration.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays > 0;
  }).length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Estoque</h1>
        <Button onClick={onAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Adicionar Item
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard title="Total de Itens" value={String(inventory.length)} icon={Archive} />
        <StatCard title="Estoque Baixo" value={String(lowStockItems)} icon={AlertTriangle} color="yellow" />
        <StatCard title="Vencendo em 30 dias" value={String(soonToExpireItems)} icon={Clock} color="purple" />
        <StatCard title="Valor Total do Estoque" value={`R$ ${totalValue.toFixed(2)}`} icon={DollarSign} color="green" />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Itens em Estoque</CardTitle>
          <CardDescription>Lista de todos os itens e medicamentos no inventário.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Categoria</TableHead>
                <TableHead>Nível do Estoque</TableHead>
                <TableHead>Custo Unit.</TableHead>
                <TableHead>Valor Total</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inventory.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{item.stock} / {item.minStock * 2}</span>
                      <Progress value={(item.stock / (item.minStock * 2)) * 100} className="w-24" />
                    </div>
                  </TableCell>
                  <TableCell>R$ {item.unitCost.toFixed(2)}</TableCell>
                  <TableCell>R$ {(item.stock * item.unitCost).toFixed(2)}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusVariant(item.status)}>
                      {getStatusText(item.status)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

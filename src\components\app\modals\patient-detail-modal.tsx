"use client";

import React from 'react';
import type { Patient } from '@/lib/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Cake, Phone, Mail, MapPin, User, Shield, Briefcase, Droplets, Stethoscope, CalendarPlus, Send } from 'lucide-react';

interface PatientDetailModalProps {
  patient: Patient;
  onClose: () => void;
  onEdit: (patient: Patient) => void;
  onScheduleAppointment: () => void;
  onSendToQueue: () => void;
}

export default function PatientDetailModal({ patient, onClose, onEdit, onScheduleAppointment, onSendToQueue }: PatientDetailModalProps) {
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>{patient.name}</DialogTitle>
          <DialogDescription>Perfil do Paciente e Resumo Médico</DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4 md:grid-cols-2">
          <div className="space-y-4">
             <h3 className="font-semibold">Informações Pessoais</h3>
             <div className="flex items-center text-sm"><User className="mr-2 h-4 w-4 text-muted-foreground" /> CPF: {patient.cpf}</div>
             <div className="flex items-center text-sm"><Cake className="mr-2 h-4 w-4 text-muted-foreground" /> {patient.birthDate} ({patient.age} anos)</div>
             <div className="flex items-center text-sm"><MapPin className="mr-2 h-4 w-4 text-muted-foreground" /> {patient.address}</div>
             <div className="flex items-center text-sm"><Mail className="mr-2 h-4 w-4 text-muted-foreground" /> {patient.email}</div>
             <div className="flex items-center text-sm"><Phone className="mr-2 h-4 w-4 text-muted-foreground" /> {patient.phone}</div>
             <div className="flex items-center text-sm"><Briefcase className="mr-2 h-4 w-4 text-muted-foreground" /> {patient.profession}</div>
          </div>
          <div className="space-y-4">
            <h3 className="font-semibold">Informações Médicas</h3>
            <div className="flex items-center text-sm"><Droplets className="mr-2 h-4 w-4 text-muted-foreground" /> Tipo Sanguíneo: <Badge variant="secondary" className="ml-2">{patient.bloodType}</Badge></div>
             <div className="flex items-center text-sm"><Shield className="mr-2 h-4 w-4 text-muted-foreground" /> Convênio: {patient.insurance}</div>
             <div>
                <p className="text-sm font-medium">Alergias</p>
                <div className="flex flex-wrap gap-1 mt-1">
                    {patient.allergies.map(allergy => <Badge key={allergy} variant="destructive">{allergy}</Badge>)}
                </div>
             </div>
             <div>
                <p className="text-sm font-medium">Histórico Médico</p>
                <p className="text-sm text-muted-foreground line-clamp-3">{patient.medicalHistory}</p>
             </div>
             <div className="border p-3 rounded-md">
                <p className="text-sm font-medium">Contato de Emergência</p>
                <p className="text-sm text-muted-foreground">{patient.emergencyContact.name} ({patient.emergencyContact.relationship})</p>
                <p className="text-sm text-muted-foreground">{patient.emergencyContact.phone}</p>
             </div>
          </div>
        </div>
        <DialogFooter className="flex-wrap justify-start sm:justify-end gap-2">
          <Button variant="outline" onClick={() => onEdit(patient)}>Editar Perfil</Button>
          <Button variant="outline" onClick={onScheduleAppointment}>
            <CalendarPlus />
            Agendar Consulta
          </Button>
          <Button onClick={onSendToQueue}>
            <Send />
            Enviar para Atendimento
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

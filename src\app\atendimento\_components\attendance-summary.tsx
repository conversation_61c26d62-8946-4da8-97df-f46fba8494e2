"use client";

import React from 'react';
import type { Patient } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FilePlus, Timer, History, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface AttendanceSummaryProps {
    patient: Patient;
}

export default function AttendanceSummary({ patient }: AttendanceSummaryProps) {
    return (
        <div className="space-y-6 h-full flex flex-col">
            <Card>
                <CardHeader>
                    <CardTitle>Resumo da Consulta</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Paciente</span>
                        <span className="font-medium">{patient.name}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Médico</span>
                        <span className="font-medium"><PERSON><PERSON><PERSON> <PERSON></span>
                    </div>
                     <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground flex items-center gap-2"><Timer className="h-4 w-4" /> Duração</span>
                        <span className="font-medium">00:15:32</span>
                    </div>
                    <Button className="w-full">Finalizar Atendimento</Button>
                </CardContent>
            </Card>

            <Card className="flex-grow">
                 <CardHeader>
                    <CardTitle>Histórico Recente</CardTitle>
                    <CardDescription>Últimas interações do paciente.</CardDescription>
                </CardHeader>
                 <CardContent className="space-y-4">
                    <div className="border-l-2 border-primary pl-3">
                        <p className="text-sm font-semibold">Última Consulta</p>
                        <p className="text-sm text-muted-foreground">{patient.lastVisit} - Queixa: Dor de cabeça</p>
                    </div>
                    <div className="border-l-2 border-muted pl-3">
                        <p className="text-sm font-semibold">Exame Solicitado</p>
                        <p className="text-sm text-muted-foreground">2024-03-10 - Hemograma completo</p>
                    </div>
                     <div className="border-l-2 border-muted pl-3">
                        <p className="text-sm font-semibold">Retorno</p>
                        <p className="text-sm text-muted-foreground">2024-01-05 - Avaliação de exames</p>
                    </div>
                    <Link href="#" className="text-sm">
                        <Button variant="link" className="p-0 h-auto">
                            Ver prontuário completo <ArrowRight className="ml-1 h-4 w-4" />
                        </Button>
                    </Link>
                </CardContent>
            </Card>

            <Button variant="outline">
                <FilePlus className="mr-2 h-4 w-4" />
                Solicitar Exames
            </Button>
        </div>
    );
}

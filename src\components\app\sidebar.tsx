"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { X, LogOut } from 'lucide-react';

const PERMISSIONS_MAP: { [key: string]: string[] } = {
    // Receptionist can see these
    dashboard: ['admin', 'professional', 'receptionist'],
    agenda: ['admin', 'professional', 'receptionist'],
    appointments: ['admin', 'professional', 'receptionist'],
    patients: ['admin', 'professional', 'receptionist'],
    notifications: ['admin', 'professional', 'receptionist'],
    
    // Professional can see these (and receptionist's)
    'medical-records': ['admin', 'professional'],

    // Ad<PERSON> can see everything
    insurances: ['admin'],
    staff: ['admin'],
    inventory: ['admin'],
    payments: ['admin'],
    reports: ['admin'],
    settings: ['admin', 'professional', 'receptionist']
};


interface SidebarProps {
  currentView: string;
  setCurrentView: (view: string) => void;
  menuItems: { id: string; label: string; icon: React.ElementType; badge?: boolean }[];
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  onLogout: () => void;
  unreadNotifications: number;
  userPermissions: string[];
}

export function Sidebar({ currentView, setCurrentView, menuItems, sidebarOpen, setSidebarOpen, onLogout, unreadNotifications, userPermissions }: SidebarProps) {

  const hasPermissionForItem = (itemId: string) => {
      const requiredPermissions = PERMISSIONS_MAP[itemId];
      if (!requiredPermissions) return true; // Default to show if not specified
      return requiredPermissions.some(p => userPermissions.includes(p));
  };

  const filteredMenuItems = menuItems.filter(item => hasPermissionForItem(item.id));

  return (
    <>
      <aside
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-card text-card-foreground shadow-lg transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0',
          { 'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen }
        )}
      >
        <div className="flex h-full flex-col">
          <div className="flex h-16 items-center justify-between border-b px-4">
            <div className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                    <span className="text-sm font-bold text-primary-foreground">CF</span>
                </div>
                <h1 className="text-xl font-bold">ClinicFlow</h1>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          <nav className="flex-1 space-y-2 p-4">
            {filteredMenuItems.map((item) => (
              <Button
                key={item.id}
                variant={currentView === item.id ? 'secondary' : 'ghost'}
                className="w-full justify-start gap-3"
                onClick={() => {
                  setCurrentView(item.id);
                  setSidebarOpen(false);
                }}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.label}</span>
                {item.id === 'notifications' && unreadNotifications > 0 && (
                  <span className="ml-auto flex h-5 min-w-[1.25rem] items-center justify-center rounded-full bg-destructive px-2 py-1 text-xs text-destructive-foreground">
                    {unreadNotifications > 99 ? '99+' : unreadNotifications}
                  </span>
                )}
              </Button>
            ))}
          </nav>

          <div className="mt-auto border-t p-4">
            {/* User area removed from here as it is now in the header */}
          </div>
        </div>
      </aside>
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </>
  );
}

"use client";

import React from 'react';
import { Hospital, MapPin, Phone } from 'lucide-react';
import type { Patient } from '@/lib/types';

export interface PrescriptionItem {
    medication: string;
    dosage: string;
}

interface PrescriptionViewProps {
    patient: Patient;
    items: PrescriptionItem[];
}

export default function PrescriptionView({ patient, items }: PrescriptionViewProps) {
    return (
        <div className="border rounded-lg bg-white dark:bg-card p-8 shadow-sm">
            {/* Header */}
            <header className="flex items-start justify-between border-b pb-4 mb-6">
                <div className="flex items-center gap-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-md bg-primary/10">
                        <Hospital className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                        <h1 className="text-2xl font-bold text-primary">ClinicFlow</h1>
                        <p className="text-sm text-muted-foreground"><PERSON>a saúde, nossa prioridade.</p>
                    </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                    <p className="flex items-center justify-end gap-2"><MapPin className="h-4 w-4" /> Rua das Flores, 123 - Medcity</p>
                    <p className="flex items-center justify-end gap-2"><Phone className="h-4 w-4" /> (11) 1234-5678</p>
                </div>
            </header>

            {/* Patient Info */}
            <section className="mb-8">
                <h2 className="text-lg font-semibold mb-2">Receituário</h2>
                <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                    <div className="font-semibold">Paciente:</div>
                    <div>{patient.name}</div>
                    <div className="font-semibold">Data:</div>
                    <div>{new Date().toLocaleDateString('pt-BR')}</div>
                </div>
            </section>
            
            {/* Body */}
            <main className="min-h-[300px] mb-8">
                 <h3 className="font-semibold text-primary mb-4 text-lg">Uso Oral</h3>
                 {items.length > 0 ? (
                    <ol className="space-y-4 list-decimal list-inside">
                       {items.map((item, index) => (
                         <li key={index} className="pl-2">
                             <p className="font-semibold text-md">{item.medication}</p>
                             <p className="text-muted-foreground ml-4">{item.dosage}</p>
                         </li>
                       ))}
                    </ol>
                 ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                        <p>Nenhum medicamento adicionado.</p>
                    </div>
                 )}
            </main>

            {/* Footer */}
            <footer className="text-center">
                <div className="w-4/5 mx-auto border-t-2 border-dotted pt-2">
                    <p className="font-bold">Dra. Maria Santos</p>
                    <p className="text-sm text-muted-foreground">Cardiologia - CRM/SP 12345</p>
                </div>
            </footer>
        </div>
    );
}

"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { InventoryItem } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';

interface InventoryModalProps {
    onClose: () => void;
    onSave: (item: Omit<InventoryItem, 'id' | 'status'>) => void;
}

export default function InventoryModal({ onClose, onSave }: InventoryModalProps) {
    const [name, setName] = useState('');
    const [category, setCategory] = useState('');
    const [stock, setStock] = useState('');
    const [minStock, setMinStock] = useState('');
    const [unitCost, setUnitCost] = useState('');
    const [supplier, setSupplier] = useState('');
    const [expirationDate, setExpirationDate] = useState('');
    const { toast } = useToast();

    const handleSubmit = () => {
        if (!name || !category || !stock || !minStock || !unitCost) {
            toast({
                variant: 'destructive',
                title: 'Campos Obrigatórios',
                description: 'Por favor, preencha todos os campos obrigatórios.'
            });
            return;
        }

        const itemData: Omit<InventoryItem, 'id' | 'status'> = {
            name,
            category,
            stock: parseInt(stock, 10),
            minStock: parseInt(minStock, 10),
            unitCost: parseFloat(unitCost),
            supplier,
            expirationDate: expirationDate || null,
        };

        onSave(itemData);
    }

    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-2xl">
                <DialogHeader>
                    <DialogTitle>Adicionar Item ao Estoque</DialogTitle>
                    <DialogDescription>
                        Insira os detalhes do novo item de estoque.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                         <div className="space-y-2">
                            <Label htmlFor="item-name">Nome do Item *</Label>
                            <Input id="item-name" value={name} onChange={e => setName(e.target.value)} placeholder="ex: Seringas 5ml" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="item-category">Categoria *</Label>
                             <Select value={category} onValueChange={setCategory}>
                                <SelectTrigger id="item-category">
                                    <SelectValue placeholder="Selecione uma categoria" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Medicamento">Medicamento</SelectItem>
                                    <SelectItem value="Material Descartável">Material Descartável</SelectItem>
                                    <SelectItem value="Equipamento">Equipamento</SelectItem>
                                    <SelectItem value="Material Cirúrgico">Material Cirúrgico</SelectItem>
                                    <SelectItem value="Produto de Limpeza">Produto de Limpeza</SelectItem>
                                    <SelectItem value="Outro">Outro</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="item-stock">Quantidade *</Label>
                            <Input id="item-stock" type="number" value={stock} onChange={e => setStock(e.target.value)} placeholder="100" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="item-min-stock">Estoque Mínimo *</Label>
                            <Input id="item-min-stock" type="number" value={minStock} onChange={e => setMinStock(e.target.value)} placeholder="20" />
                        </div>
                         <div className="space-y-2">
                            <Label htmlFor="item-unit-cost">Custo Unitário (R$) *</Label>
                            <Input id="item-unit-cost" type="number" value={unitCost} onChange={e => setUnitCost(e.target.value)} placeholder="12.50" />
                        </div>
                    </div>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="item-supplier">Fornecedor</Label>
                            <Input id="item-supplier" value={supplier} onChange={e => setSupplier(e.target.value)} placeholder="Nome do fornecedor" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="item-expiration">Data de Validade</Label>
                            <Input id="item-expiration" type="date" value={expirationDate} onChange={e => setExpirationDate(e.target.value)} />
                        </div>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button onClick={handleSubmit}>Adicionar Item</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

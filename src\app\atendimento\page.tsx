
"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { patients } from '@/lib/data';
import PatientInfo from './_components/patient-info';
import AttendanceSummary from './_components/attendance-summary';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FileText, PlusCircle, Save, Printer, ArrowLeft, Sparkles, Loader2 } from 'lucide-react';
import PrescriptionView from './_components/prescription-view';
import type { PrescriptionItem } from './_components/prescription-view';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';
import { medicalRecordSummarizer } from '@/app/actions';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export default function AttendancePage() {
    // For demonstration, we'll use the first patient from our mock data
    const patient = patients[0];
    const { toast } = useToast();
    
    // State for the prescription
    const [prescriptionItems, setPrescriptionItems] = useState<PrescriptionItem[]>([]);
    const [medication, setMedication] = useState('');
    const [dosage, setDosage] = useState('');

    // State for AI summary
    const [summary, setSummary] = useState('');
    const [isLoadingSummary, setIsLoadingSummary] = useState(false);
    const [summaryError, setSummaryError] = useState<string | null>(null);

    const handleAddPrescriptionItem = () => {
        if (medication && dosage) {
            setPrescriptionItems([...prescriptionItems, { medication, dosage }]);
            setMedication('');
            setDosage('');
             toast({
                title: "Item Adicionado",
                description: `${medication} foi adicionado à receita.`,
            });
        } else {
             toast({
                variant: 'destructive',
                title: "Campos Vazios",
                description: "Preencha o medicamento e a posologia.",
            });
        }
    };

    const handleSaveRecord = () => {
        toast({
            title: "Registro Salvo",
            description: "O registro da consulta foi salvo no prontuário do paciente.",
        });
    }
    
    const handlePrintPrescription = () => {
         toast({
            title: "Imprimindo Receita",
            description: "A receita está sendo preparada para impressão.",
        });
        // In a real app, this would trigger window.print()
    }
    
    const handleSummarize = async () => {
        setIsLoadingSummary(true);
        setSummaryError(null);
        setSummary('');

        const result = await medicalRecordSummarizer({ medicalRecord: patient.medicalHistory });
        
        if (result.success) {
            setSummary(result.summary);
        } else {
            setSummaryError(result.error || "Falha ao gerar o resumo.");
        }
        setIsLoadingSummary(false);
    };


    return (
        <div className="flex h-screen bg-muted/40 relative">
            <div className="absolute top-4 left-4 z-10">
                <Button asChild variant="outline">
                    <Link href="/">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Voltar para o Painel
                    </Link>
                </Button>
            </div>
            <main className="flex-1 p-6 grid grid-cols-1 lg:grid-cols-4 gap-6 pt-20">
                {/* Patient Info Column (Left) */}
                <div className="lg:col-span-1">
                    <PatientInfo patient={patient} />
                </div>

                {/* Main Content Column (Center) */}
                <div className="lg:col-span-2 flex flex-col gap-6">
                    <Card className="flex-grow flex flex-col">
                         <CardHeader>
                            <CardTitle>Área de Atendimento</CardTitle>
                            <CardDescription>Registre o histórico, prescreva receitas e gerencie a consulta.</CardDescription>
                        </CardHeader>
                        <CardContent className="flex-grow">
                             <Tabs defaultValue="new-entry">
                                <TabsList className="grid w-full grid-cols-3">
                                    <TabsTrigger value="new-entry">Novo Registro</TabsTrigger>
                                    <TabsTrigger value="history">Histórico</TabsTrigger>
                                    <TabsTrigger value="prescription">Prescrição</TabsTrigger>
                                </TabsList>
                                <TabsContent value="new-entry" className="mt-4">
                                    <div className="space-y-4">
                                        {summary && (
                                            <div className="p-4 border rounded-md bg-blue-50 dark:bg-blue-900/20">
                                                <h4 className="font-semibold mb-2 text-blue-800 dark:text-blue-300 flex items-center gap-2"><Sparkles className="h-4 w-4" /> Resumo do Histórico (IA)</h4>
                                                <p className="text-sm text-blue-900 dark:text-blue-200 whitespace-pre-wrap">{summary}</p>
                                            </div>
                                        )}
                                        {summaryError && (
                                            <Alert variant="destructive">
                                                <AlertTitle>Erro no Resumo</AlertTitle>
                                                <AlertDescription>{summaryError}</AlertDescription>
                                            </Alert>
                                        )}
                                        <div className="space-y-2">
                                            <Label htmlFor="queixa">Queixa Principal</Label>
                                            <Textarea id="queixa" placeholder="Descreva a queixa principal do paciente..." rows={3}/>
                                        </div>
                                        <div className="space-y-2">
                                            <Label htmlFor="diagnostico">Diagnóstico</Label>
                                            <Textarea id="diagnostico" placeholder="Adicione o diagnóstico, hipóteses..." rows={5}/>
                                        </div>
                                         <div className="space-y-2">
                                            <Label htmlFor="tratamento">Tratamento</Label>
                                            <Textarea id="tratamento" placeholder="Descreva o tratamento, orientações..." rows={4}/>
                                        </div>
                                         <div className="flex items-center gap-2">
                                            <Button className="w-full" onClick={handleSaveRecord}>
                                                <Save className="mr-2 h-4 w-4"/>
                                                Salvar Registro no Prontuário
                                            </Button>
                                            <Button variant="outline" className="w-full" onClick={handleSummarize} disabled={isLoadingSummary}>
                                                {isLoadingSummary ? (
                                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                ) : (
                                                    <Sparkles className="mr-2 h-4 w-4" />
                                                )}
                                                Resumir Histórico
                                            </Button>
                                        </div>
                                    </div>
                                </TabsContent>
                                <TabsContent value="history" className="mt-4">
                                     <ScrollArea className="h-[450px] w-full rounded-md border p-4">
                                        <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                                            {patient.medicalHistory}
                                        </div>
                                    </ScrollArea>
                                </TabsContent>
                                <TabsContent value="prescription" className="mt-4 space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="medicamento">Medicamento</Label>
                                            <Input id="medicamento" placeholder="Ex: Dipirona 500mg" value={medication} onChange={(e) => setMedication(e.target.value)} />
                                        </div>
                                         <div className="space-y-2">
                                            <Label htmlFor="posologia">Posologia</Label>
                                            <Input id="posologia" placeholder="Ex: 1 cp a cada 6h por 3 dias" value={dosage} onChange={(e) => setDosage(e.target.value)}/>
                                        </div>
                                    </div>
                                     <Button variant="outline" className="w-full" onClick={handleAddPrescriptionItem}>
                                        <PlusCircle className="mr-2 h-4 w-4"/>
                                        Adicionar à Receita
                                    </Button>

                                    <div className="space-y-4">
                                       <PrescriptionView patient={patient} items={prescriptionItems} />
                                       <Button className="w-full" onClick={handlePrintPrescription}>
                                            <Printer className="mr-2 h-4 w-4"/>
                                            Salvar e Imprimir Receita
                                        </Button>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>

                {/* Attendance Summary Column (Right) */}
                <div className="lg:col-span-1">
                    <AttendanceSummary patient={patient} />
                </div>
            </main>
        </div>
    );
}


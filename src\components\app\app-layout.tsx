"use client";

import React, { useState, useEffect } from 'react';
import { Sidebar } from '@/components/app/sidebar';
import { Header } from '@/components/app/header';
import Dashboard from '@/components/app/dashboard';
import Patients from '@/components/app/patients';
import Appointments from '@/components/app/appointments';
import Agenda from '@/components/app/agenda';
import MedicalRecords from '@/components/app/medical-records';
import Inventory from '@/components/app/inventory';
import Staff from '@/components/app/staff';
import Payments from '@/components/app/payments';
import Settings from '@/components/app/settings';
import Notifications from '@/components/app/notifications';
import Reports from '@/components/app/reports';
import { menuItems, appointments as initialAppointments, patients as initialPatients, staff as initialStaff, payments as initialPayments, inventoryItems as initialInventoryItems, notifications as initialNotifications, agendaEvents } from '@/lib/data';
import type { Patient, Appointment, Staff as StaffType, Payment, InventoryItem, Notification, AgendaEvent } from '@/lib/types';
import PatientDetailModal from '@/components/app/modals/patient-detail-modal';
import PatientFormModal from '@/components/app/modals/patient-form-modal';
import AppointmentModal from '@/components/app/modals/appointment-modal';
import StaffModal from '@/components/app/modals/staff-modal';
import InventoryModal from '@/components/app/modals/inventory-modal';
import PaymentModal from '@/components/app/modals/payment-modal';
import Insurances from './insurances';
import InsuranceModal from './modals/insurance-modal';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import MedicalRecordDetailModal from './modals/medical-record-detail-modal';
import { useRouter } from 'next/navigation';

interface AppLayoutProps {
    user: StaffType;
    onLogout: () => void;
}

const ALL_VIEWS = [
    'dashboard', 'appointments', 'agenda', 'patients', 'insurances', 
    'medical-records', 'staff', 'inventory', 'payments', 
    'reports', 'notifications', 'settings'
];

const PERMISSIONS_MAP: { [key: string]: string[] } = {
    // Receptionist can see these
    dashboard: ['admin', 'professional', 'receptionist'],
    agenda: ['admin', 'professional', 'receptionist'],
    appointments: ['admin', 'professional', 'receptionist'],
    patients: ['admin', 'professional', 'receptionist'],
    notifications: ['admin', 'professional', 'receptionist'],
    
    // Professional can see these (and receptionist's)
    'medical-records': ['admin', 'professional'],

    // Admin can see everything
    insurances: ['admin'],
    staff: ['admin'],
    inventory: ['admin'],
    payments: ['admin'],
    reports: ['admin'],
    settings: ['admin', 'professional', 'receptionist']
};


export function AppLayout({ user, onLogout }: AppLayoutProps) {
  const [currentView, setCurrentView] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const router = useRouter();
  
  const { toast } = useToast();
  
  const userPermissions = user.permissions;

  const hasPermissionForView = (view: string) => {
    const requiredPermissions = PERMISSIONS_MAP[view];
    if (!requiredPermissions) return true; // Default to allow if not specified
    return requiredPermissions.some(p => userPermissions.includes(p));
  };

  const handleSetCurrentView = (view: string) => {
    if (hasPermissionForView(view)) {
        setCurrentView(view);
    } else {
        toast({
            variant: "destructive",
            title: "Acesso Negado",
            description: "Você não tem permissão para acessar esta área.",
        });
        setCurrentView('dashboard');
    }
  };


  // State for data
  const [patients, setPatients] = useState(initialPatients);
  const [appointments, setAppointments] = useState(initialAppointments);
  const [staff, setStaff] = useState(initialStaff);
  const [payments, setPayments] = useState(initialPayments);
  const [inventory, setInventory] = useState(initialInventoryItems);
  const [notifications, setNotifications] = useState(initialNotifications);
  const [waitingQueue, setWaitingQueue] = useState<Patient[]>([]);


  // State for modals and selections
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [selectedPatientForRecord, setSelectedPatientForRecord] = useState<Patient | null>(null);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);
  const [appointmentToCancel, setAppointmentToCancel] = useState<Appointment | null>(null);
  const [editingStaff, setEditingStaff] = useState<StaffType | null>(null);
  const [patientForNewAppointment, setPatientForNewAppointment] = useState<Patient | null>(null);


  // Modal states
  const [showPatientDetailModal, setShowPatientDetailModal] = useState(false);
  const [showPatientFormModal, setShowPatientFormModal] = useState(false);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [showStaffModal, setShowStaffModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showInventoryModal, setShowInventoryModal] = useState(false);
  const [showInsuranceModal, setShowInsuranceModal] = useState(false);
  const [showMedicalRecordDetailModal, setShowMedicalRecordDetailModal] = useState(false);


  useEffect(() => {
    const isDark = localStorage.getItem('clinicflow-darkmode') === 'true';
    setDarkMode(isDark);
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, []);

  const toggleDarkMode = (isDark: boolean) => {
    setDarkMode(isDark);
    localStorage.setItem('clinicflow-darkmode', String(isDark));
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };
  
  const handleSelectPatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setShowPatientDetailModal(true);
  };

  const handleSelectPatientById = (patientId: number) => {
    const patient = patients.find(p => p.id === patientId);
    if (patient) {
      handleSelectPatient(patient);
    } else {
       toast({
        variant: "destructive",
        title: "Paciente não encontrado",
        description: "Não foi possível localizar o paciente selecionado.",
      });
    }
  };
  
  const handleEditPatient = (patient: Patient) => {
    setEditingPatient(patient);
    setShowPatientFormModal(true);
    setShowPatientDetailModal(false);
  };
  
  const handleAddNewPatient = () => {
    setEditingPatient(null);
    setShowPatientFormModal(true);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setShowAppointmentModal(true);
  }

  const handleSelectAgendaEvent = (agendaEvent: AgendaEvent) => {
    if (agendaEvent.type !== 'consulta' && agendaEvent.type !== 'procedimento') {
      toast({
        title: 'Não Editável',
        description: 'Apenas consultas e procedimentos podem ser editados diretamente pela agenda.',
      });
      return;
    }
    // Find the corresponding appointment from the main list
    const appointmentToEdit = appointments.find(a => a.id === Number(agendaEvent.id.split('-')[1]));
    if (appointmentToEdit) {
      handleEditAppointment(appointmentToEdit);
    } else {
      toast({
        variant: 'destructive',
        title: 'Consulta não encontrada',
        description: 'Não foi possível encontrar a consulta correspondente na lista principal.',
      });
    }
  };

  const handleSaveAppointment = (appointmentData: Omit<Appointment, 'id'>) => {
    if (editingAppointment) {
      // Update existing appointment
      setAppointments(prev => prev.map(apt => 
        apt.id === editingAppointment.id ? { ...apt, ...appointmentData, id: editingAppointment.id } : apt
      ));
      toast({ title: "Consulta Atualizada", description: "A consulta foi atualizada com sucesso."});
    } else {
      // Add new appointment
      const newAppointment: Appointment = {
        ...appointmentData,
        id: Date.now(), // simple unique id
      };
      setAppointments(prev => [newAppointment, ...prev]);

       const newNotification: Notification = {
        id: Date.now(),
        type: 'appointment',
        title: 'Nova Consulta Agendada',
        message: `${newAppointment.patientName} agendou com ${newAppointment.doctorName}.`,
        timestamp: new Date(),
        read: false,
        priority: 'medium',
      };
      setNotifications(prev => [newNotification, ...prev]);

      toast({ title: "Consulta Agendada", description: "A nova consulta foi agendada com sucesso."});
    }
    setEditingAppointment(null);
    setShowAppointmentModal(false);
  };

  const confirmCancelAppointment = (appointment: Appointment) => {
    setAppointmentToCancel(appointment);
  };

  const handleCancelAppointment = () => {
    if (appointmentToCancel) {
      setAppointments(prev => prev.map(apt => 
        apt.id === appointmentToCancel.id ? { ...apt, status: 'cancelado' } : apt
      ));
      toast({
        variant: 'destructive',
        title: 'Consulta Cancelada',
        description: `A consulta de ${appointmentToCancel.patientName} foi cancelada.`,
      });
      setAppointmentToCancel(null);
    }
  }

  const handleViewMedicalRecord = (patient: Patient) => {
    setSelectedPatientForRecord(patient);
    setShowMedicalRecordDetailModal(true);
  };
  
  const handleAddNewStaff = () => {
    setEditingStaff(null);
    setShowStaffModal(true);
  }

  const handleEditStaff = (staffMember: StaffType) => {
    setEditingStaff(staffMember);
    setShowStaffModal(true);
  };

  const handleSavePayment = (paymentData: Omit<Payment, 'id'>) => {
    const newPayment: Payment = {
        ...paymentData,
        id: Date.now(),
    };
    setPayments(prev => [newPayment, ...prev]);
    toast({ title: "Pagamento Registrado", description: "A transação foi registrada com sucesso." });
    setShowPaymentModal(false);
  };
  
  const handleSaveInventoryItem = (itemData: Omit<InventoryItem, 'id' | 'status'>) => {
    const newItem: InventoryItem = {
      ...itemData,
      id: Date.now(),
      status: itemData.stock > itemData.minStock ? 'ok' : itemData.stock > 0 ? 'low' : 'out',
    };
    setInventory(prev => [newItem, ...prev]);
    toast({ title: "Item Adicionado ao Estoque", description: `${itemData.name} foi adicionado com sucesso.` });
    setShowInventoryModal(false);
  };

  const handleSendToWaitingQueue = (patientId: number, doctorName?: string) => {
    const patient = patients.find(p => p.id === patientId);
    if (patient) {
      // Check if patient is already in the queue
      if (waitingQueue.some(p => p.id === patientId)) {
        toast({
            variant: "destructive",
            title: "Paciente já na Fila",
            description: `${patient.name} já está na fila de atendimento.`
        });
        return;
      }
      
      setWaitingQueue(prev => [...prev, patient]);
      
      const professional = doctorName || 'o profissional responsável';
      const newNotification: Notification = {
        id: Date.now(),
        type: 'system',
        title: 'Paciente na Fila',
        message: `${patient.name} está aguardando atendimento com ${professional}.`,
        timestamp: new Date(),
        read: false,
        priority: 'medium',
      };
      setNotifications(prev => [newNotification, ...prev]);

      toast({
        title: "Paciente na Fila",
        description: `${patient.name} foi enviado para a fila de atendimento.`,
      });
    }
  };

  const handleStartAttendanceFromQueue = (patientId: number) => {
    setWaitingQueue(prev => prev.filter(p => p.id !== patientId));
    router.push('/atendimento');
  };


  const renderContent = () => {
    if (!hasPermissionForView(currentView)) {
       return <Dashboard 
                  setCurrentView={handleSetCurrentView} 
                  waitingQueue={waitingQueue}
                  onStartAttendance={handleStartAttendanceFromQueue}
                />;
    }
    
    switch (currentView) {
      case 'dashboard':
        return <Dashboard 
                  setCurrentView={handleSetCurrentView} 
                  waitingQueue={waitingQueue}
                  onStartAttendance={handleStartAttendanceFromQueue}
                />;
      case 'appointments':
        return <Appointments 
                  appointments={appointments}
                  onAddNew={() => { setEditingAppointment(null); setShowAppointmentModal(true); }} 
                  onEdit={handleEditAppointment}
                  onCancel={confirmCancelAppointment}
                  onViewPatient={handleSelectPatientById}
                  onSendToQueue={(appointment) => handleSendToWaitingQueue(appointment.patientId, appointment.doctorName)}
                />;
      case 'agenda':
        return <Agenda 
                  onAddNewAppointment={() => setShowAppointmentModal(true)} 
                  onSelectEvent={handleSelectAgendaEvent}
                  events={agendaEvents}
               />;
      case 'patients':
        return <Patients onSelectPatient={handleSelectPatient} onAddNew={handleAddNewPatient} />;
      case 'insurances':
        return <Insurances onAddNew={() => setShowInsuranceModal(true)} />;
      case 'medical-records':
        return <MedicalRecords onViewRecord={handleViewMedicalRecord}/>;
      case 'staff':
        return <Staff staff={staff} onAddNew={handleAddNewStaff} onEdit={handleEditStaff} />;
      case 'inventory':
        return <Inventory inventory={inventory} onAddNew={() => setShowInventoryModal(true)} />;
      case 'payments':
        return <Payments payments={payments} onAddNew={() => setShowPaymentModal(true)} />;
      case 'reports':
        return <Reports />;
      case 'notifications':
        return <Notifications notifications={notifications} setNotifications={setNotifications} />;
      case 'settings':
        return <Settings onLogout={onLogout} darkMode={darkMode} setDarkMode={toggleDarkMode} user={user} />;
      default:
        return <Dashboard 
                  setCurrentView={handleSetCurrentView}
                  waitingQueue={waitingQueue}
                  onStartAttendance={handleStartAttendanceFromQueue}
                />;
    }
  };

  return (
    <div className="flex min-h-screen w-full">
      <Sidebar
        currentView={currentView}
        setCurrentView={handleSetCurrentView}
        menuItems={menuItems}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        onLogout={onLogout}
        unreadNotifications={notifications.filter(n => !n.read).length}
        userPermissions={userPermissions}
      />
      <div className="flex flex-1 flex-col">
        <Header
          user={user}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          darkMode={darkMode}
          setDarkMode={toggleDarkMode}
          onLogout={onLogout}
          setCurrentView={handleSetCurrentView}
          notifications={notifications}
          setNotifications={setNotifications}
        />
        <main className="flex-1 overflow-y-auto bg-background p-4 sm:p-6 lg:p-8">
          <div className="mx-auto max-w-7xl">
            {renderContent()}
          </div>
        </main>
      </div>

      {showPatientDetailModal && selectedPatient && (
        <PatientDetailModal
          patient={selectedPatient}
          onClose={() => setShowPatientDetailModal(false)}
          onEdit={handleEditPatient}
          onScheduleAppointment={() => {
            setPatientForNewAppointment(selectedPatient);
            setShowPatientDetailModal(false);
            setShowAppointmentModal(true);
          }}
          onSendToQueue={() => {
            setShowPatientDetailModal(false);
            handleSendToWaitingQueue(selectedPatient.id);
          }}
        />
      )}

      {showPatientFormModal && (
        <PatientFormModal 
            patient={editingPatient}
            onClose={() => {
                setShowPatientFormModal(false);
                setEditingPatient(null);
            }}
        />
      )}
      
      {showAppointmentModal && (
        <AppointmentModal 
          isOpen={showAppointmentModal}
          onClose={() => {
            setShowAppointmentModal(false);
            setEditingAppointment(null);
            setPatientForNewAppointment(null);
          }} 
          onSave={handleSaveAppointment}
          appointment={editingAppointment}
          patientForNewAppointment={patientForNewAppointment}
        />
      )}

      {appointmentToCancel && (
         <AlertDialog open onOpenChange={() => setAppointmentToCancel(null)}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmar Cancelamento</AlertDialogTitle>
                <AlertDialogDescription>
                  Você tem certeza que deseja cancelar a consulta de{" "}
                  <strong>{appointmentToCancel.patientName}</strong> em{" "}
                  {appointmentToCancel.date}? Esta ação não pode ser desfeita.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setAppointmentToCancel(null)}>Manter Consulta</AlertDialogCancel>
                <AlertDialogAction onClick={handleCancelAppointment} className="bg-destructive hover:bg-destructive/90">
                  Confirmar Cancelamento
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
      )}

      {showStaffModal && (
        <StaffModal 
            staff={editingStaff}
            onClose={() => {
                setShowStaffModal(false);
                setEditingStaff(null);
            }} 
        />
      )}

      {showInventoryModal && (
        <InventoryModal 
          onClose={() => setShowInventoryModal(false)} 
          onSave={handleSaveInventoryItem}
        />
      )}

      {showPaymentModal && (
        <PaymentModal 
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSave={handleSavePayment}
        />
      )}

      {showInsuranceModal && (
        <InsuranceModal onClose={() => setShowInsuranceModal(false)} />
      )}

      {showMedicalRecordDetailModal && selectedPatientForRecord && (
        <MedicalRecordDetailModal 
          patient={selectedPatientForRecord}
          onClose={() => setShowMedicalRecordDetailModal(false)}
        />
      )}
    </div>
  );
}

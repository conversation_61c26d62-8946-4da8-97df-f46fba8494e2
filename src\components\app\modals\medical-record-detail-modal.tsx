"use client";

import React, { useState } from 'react';
import type { Patient } from '@/lib/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Plus, Sparkles, Loader2, User } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { medicalRecordSummarizer } from '@/app/actions';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';

interface MedicalRecordDetailModalProps {
  patient: Patient;
  onClose: () => void;
}

export default function MedicalRecordDetailModal({ patient, onClose }: MedicalRecordDetailModalProps) {
  const [summary, setSummary] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddingEntry, setIsAddingEntry] = useState(false);
  const [newEntry, setNewEntry] = useState('');

  const handleSummarize = async () => {
    setIsLoading(true);
    setError(null);
    setSummary('');

    const result = await medicalRecordSummarizer({ medicalRecord: patient.medicalHistory });
    
    if (result.success) {
      setSummary(result.summary);
    } else {
      setError(result.error || "Falha ao gerar o resumo.");
    }
    setIsLoading(false);
  };
  
  const handleSaveEntry = () => {
    if (newEntry.trim()) {
        // Here you would typically save the new entry to your backend.
        // For this demo, we'll just log it and close the input.
        console.log("New Entry:", newEntry);
        // You might want to update the patient state in AppLayout here
        // e.g. patient.medicalHistory += `\n\n---\nData: ${new Date().toLocaleDateString()}\n${newEntry}`;
        setIsAddingEntry(false);
        setNewEntry('');
    }
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
            <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                    <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                    <DialogTitle className="text-2xl">Prontuário de {patient.name}</DialogTitle>
                    <DialogDescription>CPF: {patient.cpf} | Idade: {patient.age} anos</DialogDescription>
                </div>
            </div>
        </DialogHeader>
        <div className="grid gap-6 py-4 md:grid-cols-3">
            <div className="md:col-span-1 space-y-4">
                <h3 className="font-semibold text-lg">Informações Chave</h3>
                <div className="space-y-3 text-sm">
                    <div>
                        <p className="font-medium">Tipo Sanguíneo</p>
                        <p className="text-muted-foreground">{patient.bloodType}</p>
                    </div>
                     <div>
                        <p className="font-medium">Convênio</p>
                        <p className="text-muted-foreground">{patient.insurance}</p>
                    </div>
                    <div>
                        <p className="font-medium">Alergias</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                            {patient.allergies.map(allergy => <Badge key={allergy} variant="destructive" className="text-xs">{allergy}</Badge>)}
                        </div>
                    </div>
                    <div>
                        <p className="font-medium">Contato de Emergência</p>
                        <p className="text-muted-foreground">{patient.emergencyContact.name} ({patient.emergencyContact.relationship})</p>
                        <p className="text-muted-foreground">{patient.emergencyContact.phone}</p>
                    </div>
                </div>

                <div className="space-y-2 mt-4">
                    <Button className="w-full" onClick={handleSummarize} disabled={isLoading}>
                    {isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                        <Sparkles className="mr-2 h-4 w-4" />
                    )}
                    Resumir com IA
                    </Button>
                     <Button variant="outline" className="w-full" onClick={() => setIsAddingEntry(!isAddingEntry)}>
                        <Plus className="mr-2 h-4 w-4" />
                        {isAddingEntry ? 'Cancelar' : 'Adicionar Registro'}
                    </Button>
                </div>
            </div>
            <div className="md:col-span-2 space-y-4">
                 <h3 className="font-semibold text-lg">Histórico Médico</h3>
                 {summary && (
                    <div className="p-4 border rounded-md bg-blue-50 dark:bg-blue-900/20">
                        <h4 className="font-semibold mb-2 text-blue-800 dark:text-blue-300 flex items-center gap-2"><Sparkles className="h-4 w-4" /> Resumo da IA</h4>
                        <p className="text-sm text-blue-900 dark:text-blue-200 whitespace-pre-wrap">{summary}</p>
                    </div>
                 )}
                 {error && (
                    <Alert variant="destructive">
                        <AlertTitle>Erro</AlertTitle>
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}
                 {isAddingEntry && (
                    <div className="space-y-2 p-4 border-dashed border-2 rounded-lg">
                         <Label htmlFor="new-entry" className="text-base font-semibold">Nova Entrada no Prontuário</Label>
                         <Textarea 
                            id="new-entry"
                            value={newEntry}
                            onChange={(e) => setNewEntry(e.target.value)}
                            placeholder={`Adicione uma nova entrada para o prontuário de ${patient.name}...\nEx: Data, Queixa, Diagnóstico, Tratamento.`}
                            rows={6}
                         />
                         <div className="flex justify-end gap-2 pt-2">
                             <Button variant="ghost" onClick={() => setIsAddingEntry(false)}>Cancelar</Button>
                             <Button onClick={handleSaveEntry}>Salvar Registro</Button>
                         </div>
                    </div>
                )}
                <ScrollArea className="h-96 w-full rounded-md border p-4">
                    <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                        {patient.medicalHistory}
                    </div>
                </ScrollArea>
            </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

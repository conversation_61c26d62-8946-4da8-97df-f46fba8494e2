'use server';
/**
 * @fileOverview A medical record summarization AI agent.
 *
 * - medicalRecordSummarizer - A function that handles the summarization process.
 * - MedicalRecordSummarizerInput - The input type for the medicalRecordSummarizer function.
 * - MedicalRecordSummarizerOutput - The return type for the medicalRecordSummarizer function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const MedicalRecordSummarizerInputSchema = z.object({
  medicalRecord: z.string().describe('The medical record to summarize.'),
});
export type MedicalRecordSummarizerInput = z.infer<typeof MedicalRecordSummarizerInputSchema>;

const MedicalRecordSummarizerOutputSchema = z.object({
  summary: z.string().describe('The summary of the medical record.'),
});
export type MedicalRecordSummarizerOutput = z.infer<typeof MedicalRecordSummarizerOutputSchema>;

export async function medicalRecordSummarizer(input: MedicalRecordSummarizerInput): Promise<MedicalRecordSummarizerOutput> {
  return medicalRecordSummarizerFlow(input);
}

const prompt = ai.definePrompt({
  name: 'medicalRecordSummarizerPrompt',
  input: {schema: MedicalRecordSummarizerInputSchema},
  output: {schema: MedicalRecordSummarizerOutputSchema},
  prompt: `You are an expert medical summarizer. Please summarize the following medical record.  The summary should be concise and easy to understand.

Medical Record: {{{medicalRecord}}}`,
});

const medicalRecordSummarizerFlow = ai.defineFlow(
  {
    name: 'medicalRecordSummarizerFlow',
    inputSchema: MedicalRecordSummarizerInputSchema,
    outputSchema: MedicalRecordSummarizerOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

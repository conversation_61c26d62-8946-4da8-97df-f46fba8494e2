"use client";

import React from 'react';
import { patients } from '@/lib/data';
import type { Patient } from '@/lib/types';
import PatientCard from './patient-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus, Search, Filter, Download } from 'lucide-react';

interface PatientsProps {
  onSelectPatient: (patient: Patient) => void;
  onAddNew: () => void;
}

export default function Patients({ onSelectPatient, onAddNew }: PatientsProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Cadastro de Pacientes</h1>
        <Button onClick={onAddNew}>
          <UserPlus className="mr-2 h-4 w-4" />
          Novo Paciente
        </Button>
      </div>

      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
            <div className="relative w-full md:w-auto md:flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                    type="text"
                    placeholder="Buscar pacientes por nome ou CPF..."
                    className="pl-10 w-full"
                />
            </div>
            <div className="flex items-center gap-2">
                <Select>
                    <SelectTrigger className="w-full md:w-[180px]">
                        <SelectValue placeholder="Filtrar por status" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="active">Ativo</SelectItem>
                        <SelectItem value="inactive">Inativo</SelectItem>
                    </SelectContent>
                </Select>
                 <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                 </Button>
                 <Button variant="outline" size="icon">
                    <Download className="h-4 w-4" />
                 </Button>
            </div>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {patients.map((patient) => (
            <PatientCard key={patient.id} patient={patient} onSelectPatient={onSelectPatient} />
          ))}
        </div>
      </div>
    </div>
  );
}

"use client";

import React from 'react';
import { StatCard } from './stat-card';
import { BarChart, Users, Calendar, DollarSign, Activity, TrendingUp, Send, Stethoscope } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { appointments, patients } from '@/lib/data';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import type { Patient } from '@/lib/types';

interface DashboardProps {
    setCurrentView: (view: string) => void;
    waitingQueue: Patient[];
    onStartAttendance: (patientId: number) => void;
}

export default function Dashboard({ setCurrentView, waitingQueue, onStartAttendance }: DashboardProps) {
  const upcomingBirthdays = patients.filter(p => p.isBirthday || p.birthdayThisWeek);
  
  return (
    <div className="space-y-6">
       <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <Button onClick={() => setCurrentView('reports')}>
          <TrendingUp className="mr-2 h-4 w-4" />
          Gerar Relatório
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard title="Total de Pacientes" value="1.247" icon={Users} change="+12%" />
        <StatCard title="Na Fila de Atendimento" value={String(waitingQueue.length)} icon={Users} color="yellow" />
        <StatCard title="Receita Mensal" value="R$ 12.350" icon={DollarSign} change="+15%" color="green" />
        <StatCard title="Consultas Hoje" value="8" icon={Activity} color="purple" />
      </div>

       <Card>
          <CardHeader>
            <CardTitle>Fila de Atendimento</CardTitle>
            <CardDescription>Pacientes aguardando para serem atendidos.</CardDescription>
          </CardHeader>
          <CardContent>
            {waitingQueue.length > 0 ? (
                <div className="space-y-4">
                {waitingQueue.map((patient) => (
                    <div key={patient.id} className="flex items-center">
                    <Avatar className="h-9 w-9">
                        <AvatarImage src={patient.avatar} data-ai-hint="patient portrait" />
                        <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="ml-4 space-y-1">
                        <p className="text-sm font-medium leading-none">{patient.name}</p>
                        <p className="text-sm text-muted-foreground">{patient.insurance || 'Particular'}</p>
                    </div>
                    <Button className="ml-auto" size="sm" onClick={() => onStartAttendance(patient.id)}>
                        <Stethoscope className="mr-2 h-4 w-4"/>
                        Atender Paciente
                    </Button>
                    </div>
                ))}
                </div>
            ) : (
                <div className="flex flex-col items-center justify-center h-full text-center py-8">
                    <Users className="w-12 h-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">A fila de atendimento está vazia.</p>
                </div>
            )}
          </CardContent>
        </Card>


      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Consultas Recentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {appointments.slice(0, 4).map((appointment) => (
                <div key={appointment.id} className="flex items-center">
                  <Avatar className="h-9 w-9">
                    <AvatarImage src={`https://placehold.co/100x100.png`} data-ai-hint="patient portrait" />
                    <AvatarFallback>{appointment.patientName.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">{appointment.patientName}</p>
                    <p className="text-sm text-muted-foreground">{appointment.specialty}</p>
                  </div>
                  <div className="ml-auto font-medium">{appointment.time}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>🎂 Próximos Aniversários</CardTitle>
          </CardHeader>
          <CardContent>
             {upcomingBirthdays.length > 0 ? (
                <div className="space-y-4">
                {upcomingBirthdays.slice(0, 4).map((patient) => (
                    <div key={patient.id} className="flex items-center">
                    <Avatar className="h-9 w-9">
                        <AvatarImage src={`https://placehold.co/100x100.png`} data-ai-hint="happy person" />
                        <AvatarFallback>{patient.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="ml-4 space-y-1">
                        <p className="text-sm font-medium leading-none">{patient.name}</p>
                        <p className="text-sm text-muted-foreground">
                            Faz {patient.age + 1} anos em {new Date(patient.birthDate).toLocaleDateString('pt-BR', { month: 'long', day: 'numeric' })}
                        </p>
                    </div>
                    {patient.isBirthday && <Badge className="ml-auto bg-accent text-accent-foreground">Hoje!</Badge>}
                    </div>
                ))}
                </div>
             ) : (
                <div className="flex flex-col items-center justify-center h-full text-center">
                    <Calendar className="w-12 h-12 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">Nenhum aniversário esta semana.</p>
                </div>
             )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

"use client";

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface InsuranceModalProps {
    onClose: () => void;
}

export default function InsuranceModal({ onClose }: InsuranceModalProps) {
    return (
        <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-lg">
                <DialogHeader>
                    <DialogTitle>Novo Convênio</DialogTitle>
                    <DialogDescription>
                        Preencha os detalhes do novo convênio.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="insurance-name">Nome do Convênio</Label>
                        <Input id="insurance-name" placeholder="ex: Amil, Unimed" />
                    </div>
                     <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="insurance-cnpj">CNPJ</Label>
                            <Input id="insurance-cnpj" placeholder="00.000.000/0001-00" />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="insurance-phone">Telefone</Label>
                            <Input id="insurance-phone" type="tel" placeholder="(11) 99999-9999" />
                        </div>
                    </div>
                     <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                         <Select>
                            <SelectTrigger id="status">
                                <SelectValue placeholder="Selecione o status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ativo">Ativo</SelectItem>
                                <SelectItem value="inativo">Inativo</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button type="submit">Adicionar Convênio</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

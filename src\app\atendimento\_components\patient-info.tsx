"use client";

import React from 'react';
import type { Patient } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Cake, Phone, Shield, Droplets, AlertTriangle, User, UserCircle } from 'lucide-react';

interface PatientInfoProps {
    patient: Patient;
}

export default function PatientInfo({ patient }: PatientInfoProps) {
    return (
        <Card className="h-full">
            <CardHeader className="flex flex-col items-center text-center">
                 <Avatar className="h-24 w-24 mb-2 border-4 border-primary/20">
                    <AvatarImage src={patient.avatar} data-ai-hint="patient photo" />
                    <AvatarFallback className="text-3xl">
                        <UserCircle />
                    </AvatarFallback>
                </Avatar>
                <CardTitle className="text-xl">{patient.name}</CardTitle>
                <CardDescription>Paciente desde {new Date(patient.lastVisit).getFullYear()}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-2"><User className="h-4 w-4 text-muted-foreground" /> CPF: {patient.cpf}</div>
                    <div className="flex items-center gap-2"><Cake className="h-4 w-4 text-muted-foreground" /> {patient.birthDate} ({patient.age} anos)</div>
                    <div className="flex items-center gap-2"><Shield className="h-4 w-4 text-muted-foreground" /> Convênio: {patient.insurance}</div>
                    <div className="flex items-center gap-2"><Droplets className="h-4 w-4 text-muted-foreground" /> Tipo Sanguíneo: <Badge variant="secondary" className="ml-1">{patient.bloodType}</Badge></div>
                </div>
                <div className="space-y-2">
                    <h4 className="font-semibold flex items-center gap-2"><AlertTriangle className="h-4 w-4 text-destructive" /> Alergias</h4>
                    <div className="flex flex-wrap gap-1">
                        {patient.allergies.length > 0 ? (
                            patient.allergies.map(allergy => <Badge key={allergy} variant="destructive">{allergy}</Badge>)
                        ) : (
                            <p className="text-sm text-muted-foreground">Nenhuma alergia registrada.</p>
                        )}
                    </div>
                </div>
                 <div className="space-y-2 pt-2">
                    <h4 className="font-semibold">Contato de Emergência</h4>
                    <div className="text-sm text-muted-foreground">
                        <p>{patient.emergencyContact.name} ({patient.emergencyContact.relationship})</p>
                        <p className="flex items-center gap-2 mt-1"><Phone className="h-4 w-4" /> {patient.emergencyContact.phone}</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

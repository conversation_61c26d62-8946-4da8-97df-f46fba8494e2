"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { patients } from '@/lib/data';
import type { Payment } from '@/lib/types';
import { useState } from 'react';

interface PaymentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (payment: Omit<Payment, 'id'>) => void;
}

export default function PaymentModal({ isOpen, onClose, onSave }: PaymentModalProps) {
    const [patientId, setPatientId] = useState<string>('');
    const [amount, setAmount] = useState<string>('');
    const [method, setMethod] = useState<Payment['method'] | ''>('');
    const [service, setService] = useState<string>('');


    const handleSubmit = () => {
        const patient = patients.find(p => p.id === Number(patientId));
        if (patient && amount && method && service) {
            const paymentData: Omit<Payment, 'id'> = {
                patientName: patient.name,
                service: service,
                amount: parseFloat(amount),
                method: method as Payment['method'],
                date: new Date().toLocaleDateString('pt-BR'),
                status: 'pago',
            };
            onSave(paymentData);
            setPatientId('');
            setAmount('');
            setMethod('');
            setService('');
        } else {
            // TODO: Add proper validation feedback
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-lg">
                <DialogHeader>
                    <DialogTitle>Registrar Pagamento</DialogTitle>
                    <DialogDescription>
                       Registre uma nova transação de pagamento.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="payment-patient">Paciente</Label>
                        <Select value={patientId} onValueChange={setPatientId}>
                            <SelectTrigger id="payment-patient">
                                <SelectValue placeholder="Selecione um paciente" />
                            </SelectTrigger>
                            <SelectContent>
                                {patients.map(p => <SelectItem key={p.id} value={String(p.id)}>{p.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                     <div className="space-y-2">
                        <Label htmlFor="payment-service">Serviço/Procedimento</Label>
                        <Input id="payment-service" value={service} onChange={(e) => setService(e.target.value)} placeholder="ex: Consulta, Exame" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="payment-amount">Valor (R$)</Label>
                        <Input id="payment-amount" type="number" value={amount} onChange={(e) => setAmount(e.target.value)} placeholder="150,00" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="payment-method">Método de Pagamento</Label>
                         <Select value={method} onValueChange={(value) => setMethod(value as Payment['method'])}>
                            <SelectTrigger id="payment-method">
                                <SelectValue placeholder="Selecione o método de pagamento" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="PIX">PIX</SelectItem>
                                <SelectItem value="Cartão de Crédito">Cartão de Crédito</SelectItem>
                                <SelectItem value="Cartão de Débito">Cartão de Débito</SelectItem>
                                <SelectItem value="Dinheiro">Dinheiro (à vista)</SelectItem>
                                <SelectItem value="Convênio">Convênio</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button onClick={handleSubmit}>Registrar Pagamento</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
